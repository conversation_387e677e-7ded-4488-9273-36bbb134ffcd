{"output": {"filePath": "repomix-output.md", "style": "plain", "parsableStyle": true, "compress": true, "headerText": "FastScan - Project Overview", "fileSummary": true, "directoryStructure": true, "removeComments": false, "removeEmptyLines": false, "topFilesLength": 0, "showLineNumbers": false, "copyToClipboard": false, "includeEmptyDirectories": false, "git": {"sortByChanges": false}}, "include": ["**/*"], "ignore": {"useGitignore": true, "useDefaultPatterns": true, "customPatterns": ["tmp/", "*.log", "node_modules/", ".git/", "coverage/", "dist/", "build/", ".terraform/", ".terraform.lock.hcl", "*.zip", "templates/", "bin/", "ice_templates.csv", "nuclei.zip", "nuclei-templates.zip", "nuclei-configs.zip", "terraform.tfstate", "terraform.tfstate.backup"]}, "security": {"enableSecurityCheck": true}}