# Lambda-Nuclei-Scanner

A dual-mode Nuclei vulnerability scanner that runs both as an AWS Lambda function and as a standalone CLI executable.

## Overview

This project provides a unified execution engine for Nuclei vulnerability scanning that can operate in two modes:

1. **AWS Lambda Mode**: Serverless execution for cloud-based scanning at scale
2. **CLI Mode**: Standalone executable (`nuclei_local_runner`) for local execution

The same codebase powers both modes, ensuring consistent behavior and results whether running locally or in the cloud. This is the execution engine used by Nuclear Pond for both local and cloud scanning operations.

## Features

- **Dual-Mode Operation**: Runs as AWS Lambda function or standalone CLI executable
- **Consistent API**: Same interface and behavior in both modes
- **Multiple Output Formats**: Supports JSON, S3 upload, and command output
- **Template Management**: Uses Nuclei's native template discovery and management
- **S3 Integration**: Automatic upload of findings to S3 with organized partitioning (Lambda mode)
- **Configuration Management**: Centralized Nuclei configuration
- **Error Handling**: Comprehensive error handling and logging
- **Nuclear Pond Integration**: Designed as the execution engine for Nuclear Pond orchestrator

## Architecture

### Lambda Mode
The Lambda function consists of:
1. **Main Function**: Go-based Lambda handler (`main.go`)
2. **Lambda Layers**:
   - Nuclei binary layer
   - Custom templates layer
   - Configuration files layer
3. **S3 Storage**: Artifacts bucket for binaries, templates, and findings
4. **IAM Permissions**: Least-privilege access to required AWS services

### CLI Mode
The standalone executable consists of:
1. **Main Function**: Same Go code with CLI argument parsing
2. **Local Nuclei**: Uses system-installed Nuclei binary
3. **Local Templates**: Uses Nuclei's standard template discovery
4. **File Output**: Direct file system output for results

## Input Event Format

```json
{
  "Targets": [
    "http://test4.fast-scan-demo-target.click", "http://test1.fast-scan-demo-target.click", "http://test2.fast-scan-demo-target.click", "http://test3.fast-scan-demo-target.click", "http://test5.fast-scan-demo-target.click", "http://test6.fast-scan-demo-target.click", "http://test7.fast-scan-demo-target.click", "http://test8.fast-scan-demo-target.click", "http://test9.fast-scan-demo-target.click", "http://test10.fast-scan-demo-target.click"
  ],
  "Args": [],
  "Output": "s3"
}
```

### Parameters

- **targets** (required): Array of target URLs or IP addresses to scan
- **args** (optional): Additional Nuclei command-line arguments
- **output** (optional): Output format - "s3", "json", or "cmd" (default)

## Output Formats

### S3 Output (`"output": "s3"`)
Uploads findings to S3 with organized partitioning:
```
s3://bucket/findings/YYYY/MM/DD/HH/nuclei-findings-{uuid}.json
```

### JSON Output (`"output": "json"`)
Returns raw JSON findings directly in the Lambda response.

### Command Output (`"output": "cmd"`)
Returns base64-encoded command output (stdout/stderr).

## Environment Variables

- **BUCKET_NAME**: S3 bucket name for storing findings (required for S3 output)
- **AWS_REGION**: AWS region (automatically set by Lambda runtime)
- **HOME**: Set to `/tmp` for Nuclei configuration (automatically configured)

## Building

### Prerequisites

- Go 1.19 or later
- Node.js and npm (for build scripts)
- AWS CLI configured (for Lambda deployment)
- Nuclei binary installed and in PATH (for CLI mode)

### Build for Lambda Runtime

```bash
# Build Lambda function
npm run build
# Creates 'bootstrap' executable for Lambda deployment
```

### Build for CLI/Local Runtime

```bash
# Build standalone CLI executable
npm run build:standalone
# Creates 'nuclei_local_runner' executable for local use
```

### Manual Building

```bash
# Lambda build
GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o bootstrap

# CLI build
go build -o nuclei_local_runner
```

## CLI Usage

The `nuclei_local_runner` executable supports standalone CLI usage for local development and testing. See [`CLI_USAGE.md`](CLI_USAGE.md) for detailed documentation.

### Quick CLI Examples

```bash
# Scan a single target
./nuclei_local_runner -targets "http://g2a349pb.fast-scan-demo-target.click" -args "-t dns"

# Scan multiple targets with custom arguments
./nuclei_local_runner -targets "site1.com,site2.com" -args "-severity high -t cves"

# Scan from file and save results
./nuclei_local_runner -target-file targets.txt -args "-t dns" -output-file results.json
```

## Testing

```bash
# Install dependencies
go mod download

# Run tests
go test ./...

# Test CLI mode locally (requires Nuclei in PATH)
./nuclei_local_runner -targets "example.com" -args "-t dns"
```

## Deployment

This Lambda function is primarily deployed via Terraform as part of the `nuclei_lambda` module, which handles the AWS infrastructure setup. For details on infrastructure deployment, refer to the `terraform/nuclei_lambda/README.md` documentation.

### Manual Deployment (Alternative)

If you need to deploy the Lambda package manually (e.g., for quick testing or specific scenarios outside of Terraform management):

1. Build the Go binary: `GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o bootstrap`
2. Create deployment package: `zip lambda.zip bootstrap`
3. Upload to AWS Lambda with runtime `provided.al2`

## Configuration

### Nuclei Configuration

The function uses a centralized Nuclei configuration file deployed via Lambda layers. The configuration is located at `/opt/nuclei-config.yaml` in the Lambda runtime.

### Custom Templates

Custom Nuclei templates are deployed via Lambda layers and available in the Lambda runtime. Template paths are configured in the Nuclei configuration file.

## Monitoring

The Lambda function provides comprehensive logging via CloudWatch:

- Execution logs with detailed error information
- Performance metrics (duration, memory usage)
- Custom metrics for scan results and errors

## Security

- **Least Privilege**: IAM role with minimal required permissions
- **Network Isolation**: Can be deployed in VPC for enhanced security
- **Encryption**: S3 storage encrypted at rest
- **Input Validation**: Comprehensive input validation and sanitization

## Troubleshooting

### Common Issues

#### Lambda Mode
1. **Build Failures**: Ensure Go 1.19+ and correct GOOS/GOARCH settings for Lambda
2. **Permission Errors**: Verify IAM role has required S3 and CloudWatch permissions
3. **Timeout Issues**: Increase Lambda timeout for large scans
4. **Memory Issues**: Increase Lambda memory allocation

#### CLI Mode
1. **Build Failures**: Ensure Go 1.19+ is installed
2. **Nuclei Not Found**: Ensure Nuclei is installed and in system PATH
3. **Permission Errors**: Ensure `nuclei_local_runner` has execute permissions (`chmod +x`)
4. **Missing Dependencies**: Run `go mod download` before building

### Debugging

#### Lambda Mode
Enable detailed logging by setting log level in the Nuclei configuration:

```yaml
# nuclei-config.yaml
log-level: debug
```

#### CLI Mode
Use verbose Nuclei arguments for detailed output:

```bash
./nuclei_local_runner -targets "example.com" -args "-t dns -v"
```

## Integration with Nuclear Pond

This execution engine is designed to work seamlessly with Nuclear Pond:

- **Local Mode**: Nuclear Pond starts `nuclei_local_runner` as subprocesses
- **Cloud Mode**: Nuclear Pond invokes the Lambda function
- **Consistent API**: Both modes use the same input/output format
- **Unified Results**: Results format is identical across both execution modes

For Nuclear Pond integration details, see the [Nuclear Pond documentation](../nuclear_pond/docs/).

## Dependencies

- `github.com/aws/aws-lambda-go`: AWS Lambda Go runtime
- `github.com/aws/aws-sdk-go`: AWS SDK for S3 operations
- `github.com/google/uuid`: UUID generation for unique filenames

## License

This project is licensed under the MIT License - see the main project LICENSE file for details.
