# Build stage
FROM golang:1.22-alpine AS build

WORKDIR /app

# Copy Go modules files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY main.go ./

# Build for Lambda runtime
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o bootstrap main.go

# Runtime stage - use AWS Lambda base image for testing
FROM public.ecr.aws/lambda/provided:al2

# Copy the built binary
COPY --from=build /app/bootstrap ${LAMBDA_RUNTIME_DIR}/bootstrap

# Set the CMD to your handler
<PERSON><PERSON> ["bootstrap"]
