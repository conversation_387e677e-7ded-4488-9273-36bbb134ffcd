package scanner

import (
	"fmt"
	"log"
	"os"

	"main/internal/aws"
	"main/internal/config"
	"main/internal/nuclei"
	"main/internal/types"
)

// Scanner provides the main interface for Nuclei scanning operations
type Scanner struct {
	env *types.Environment
}

// NewScanner creates a new scanner instance with the appropriate environment
func NewScanner() (*Scanner, error) {
	var env *types.Environment
	var err error

	if config.IsLambdaEnvironment() {
		env, err = config.SetupLambdaEnvironment()
		if err != nil {
			return nil, fmt.Errorf("failed to setup Lambda environment: %v", err)
		}
		log.Printf("Lambda environment configured successfully. Version is %s", types.LambdaVersion)
	} else {
		env, err = config.SetupLocalEnvironment()
		if err != nil {
			return nil, fmt.Errorf("failed to setup local environment: %v", err)
		}
		log.Printf("Local environment configured successfully")
	}

	return &Scanner{env: env}, nil
}

// GetEnvironment returns the current environment configuration
func (s *Scanner) GetEnvironment() *types.Environment {
	return s.env
}

// ValidateEnvironment validates the scanner environment and nuclei binary
func (s *Scanner) ValidateEnvironment() error {
	return nuclei.ValidateBinary(s.env)
}

// NormalizeRequest normalizes the scan request by handling field variants
func (s *Scanner) NormalizeRequest(request *types.ScanRequest) {
	// Handle field name variants and log which was used
	if len(request.Targets) == 0 && len(request.TargetsUpper) > 0 {
		request.Targets = request.TargetsUpper
		log.Printf("Using TargetsUpper field with %d targets", len(request.Targets))
	}
	if len(request.Args) == 0 && len(request.ArgsUpper) > 0 {
		request.Args = request.ArgsUpper
	}
	if request.Output == "" && request.OutputUpper != "" {
		request.Output = request.OutputUpper
	}
}

// Scan executes a nuclei scan with the given request
func (s *Scanner) Scan(request *types.ScanRequest) (*types.ScanResponse, error) {
	// Normalize request
	s.NormalizeRequest(request)

	// Log received request for debugging
	log.Printf("Scanner received request: Targets=%d, Args=%v, Output=%s",
		len(request.Targets), request.Args, request.Output)

	// Validate environment
	if err := s.ValidateEnvironment(); err != nil {
		return &types.ScanResponse{
			Error: fmt.Sprintf("Environment validation failed: %v", err),
		}, err
	}

	// Prepare nuclei command
	cmdArgs, err := nuclei.PrepareCommand(s.env, request)
	if err != nil {
		return &types.ScanResponse{
			Error: fmt.Sprintf("Command preparation failed: %v", err),
		}, err
	}

	// Execute nuclei with validation
	result, err := nuclei.ExecuteWithValidation(s.env, cmdArgs, request.Output)
	if err != nil {
		return &types.ScanResponse{
			Output: result.Output,
			Error:  fmt.Sprintf("Nuclei execution failed: %v", err),
		}, err
	}

	// Process output based on mode
	finalOutput, responseError := s.ProcessOutput(result, request.Output)

	// Cleanup
	s.Cleanup(request, result)

	return &types.ScanResponse{
		Output: finalOutput,
		Error:  responseError,
	}, nil
}

// ProcessOutput processes the nuclei result based on output mode
func (s *Scanner) ProcessOutput(result *types.NucleiResult, outputMode string) (string, string) {
	switch outputMode {
	case types.OutputModeS3:
		return aws.ProcessS3Output(result)
	case types.OutputModeJSON:
		return nuclei.ProcessJSONOutput(result)
	default:
		// CMD mode
		return nuclei.ProcessOutput(result, outputMode)
	}
}

// Cleanup performs post-scan cleanup
func (s *Scanner) Cleanup(request *types.ScanRequest, result *types.NucleiResult) {
	// Remove targets file if multiple targets were used
	if len(request.Targets) > 1 {
		os.Remove(s.env.TargetsFile)
	}
	// Remove scan output file
	os.Remove(s.env.ScanOutput)
}
