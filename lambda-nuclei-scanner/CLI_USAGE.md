# Nuclei Local Runner - CLI Usage

The lambda-nuclei-scanner supports both AWS Lambda execution and standalone CLI execution. This document covers the CLI mode, which is used by Nuclear Pond for local execution.

## Overview

The `nuclei_local_runner` executable provides a standalone interface to Nuclei scanning that:
- Uses the same codebase as the Lambda function for consistency
- Accepts the same input format as the Lambda version
- Provides identical output formatting
- Serves as the execution engine for Nuclear Pond's local mode

## Building the Standalone Executable

```bash
# Using npm script (recommended)
npm run build:standalone

# Manual build
go build -o nuclei_local_runner
```

This creates a `nuclei_local_runner` executable that can be used independently or by Nuclear Pond.

## CLI Usage

### Basic Usage

```bash
# Scan a single target
./nuclei_local_runner -targets "example.com" -args "-t dns"

# Scan multiple targets (comma-separated)
./nuclei_local_runner -targets "example.com,httpbin.org" -args "-t dns -severity high"

# Scan targets from a file
./nuclei_local_runner -target-file targets.txt -args "-t dns"

# Save output to a file
./nuclei_local_runner -targets "example.com" -args "-t dns" -output-file results.json
```

### Command Line Arguments

- `-targets`: Comma-separated list of targets to scan
- `-target-file`: File containing targets to scan (one per line)
- `-args`: Additional nuclei arguments (e.g., "-t dns -severity high")
- `-output-file`: Output file for JSON results (optional, defaults to stdout)

### Examples

1. **DNS enumeration on a single target:**
   ```bash
   ./nuclei_local_runner -targets "example.com" -args "-t dns"
   ```

2. **High severity vulnerabilities on multiple targets:**
   ```bash
   ./nuclei_local_runner -targets "site1.com,site2.com" -args "-severity high"
   ```

3. **Scan from file with custom templates:**
   ```bash
   ./nuclei_local_runner -target-file my_targets.txt -args "-t /path/to/templates/"
   ```

4. **Save results to file:**
   ```bash
   ./nuclei_local_runner -targets "example.com" -args "-t dns" -output-file scan_results.json
   ```

## Target File Format

The target file should contain one target per line:

```
example.com
httpbin.org
# Comments are ignored
another-site.com
```

## Prerequisites

- **Nuclei**: Must be installed and available in system PATH
- **Go**: Version 1.19+ for building from source
- **Permissions**: Execute permissions on the built executable

```bash
# Verify Nuclei installation
nuclei -version

# Set execute permissions if needed
chmod +x nuclei_local_runner
```

## Environment Detection

The application automatically detects whether it's running in:
- **AWS Lambda environment**: Uses Lambda handler for serverless execution
- **Local environment**: Uses CLI mode with command-line arguments

## Integration with Nuclear Pond

Nuclear Pond uses `nuclei_local_runner` as a subprocess for local execution:

1. Nuclear Pond starts one or more `nuclei_local_runner` processes
2. Each process receives targets and Nuclei arguments
3. The subprocess executes Nuclei and returns results
4. Nuclear Pond aggregates results from all subprocesses

This provides:
- Consistent behavior between local and cloud modes
- Parallel execution through multiple subprocess instances
- Unified API interface for both execution environments

## Advanced Usage

### Custom Template Paths

```bash
# Use custom templates directory
./nuclei_local_runner -targets "example.com" -args "-tp /path/to/custom/templates -t custom"
```

### Performance Tuning

```bash
# High-performance scanning with rate limiting
./nuclei_local_runner -targets "example.com" -args "-t cves -rate-limit 100 -c 50"
```

### Output Formats

```bash
# JSON output to file
./nuclei_local_runner -targets "example.com" -args "-t dns -o results.json -oj"

# Silent mode with specific severity
./nuclei_local_runner -targets "example.com" -args "-t cves -severity critical -silent"
```

## Building for Lambda

```bash
npm run build  # Creates 'bootstrap' for Lambda deployment
```

## Troubleshooting

### Common Issues

1. **Nuclei not found**: Ensure Nuclei is in PATH
2. **Permission denied**: Run `chmod +x nuclei_local_runner`
3. **No output**: Check Nuclei arguments and target accessibility
4. **Build failures**: Ensure Go 1.19+ is installed

### Debug Mode

```bash
# Enable verbose output
./nuclei_local_runner -targets "example.com" -args "-t dns -v"

# Debug with specific templates
./nuclei_local_runner -targets "example.com" -args "-t dns -debug"
```