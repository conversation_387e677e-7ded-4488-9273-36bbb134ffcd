package types

// ScanRequest represents the input for a nuclei scan
type ScanRequest struct {
	Targets []string `json:"targets"`
	Args    []string `json:"args"`
	Output  string   `json:"output"`

	// Support uppercase variants for compatibility
	TargetsUpper []string `json:"Targets"`
	ArgsUpper    []string `json:"Args"`
	OutputUpper  string   `json:"Output"`
}

// ScanResponse represents the output of a nuclei scan
type ScanResponse struct {
	Output string `json:"output"`
	Error  string `json:"error"`
}

// NucleiResult encapsulates the result of a nuclei execution
type NucleiResult struct {
	Output     string
	HasResults bool
	OutputFile string
	Error      error
}

// CLIConfig holds configuration for CLI execution
type CLIConfig struct {
	Targets    string
	TargetFile string
	Args       string
	OutputFile string
}

// Environment represents the runtime environment configuration
type Environment struct {
	NucleiBinary     string
	FileSystem       string
	TargetsFile      string
	ScanOutput       string
	NucleiConfigPath string
	IsLambda         bool
}

// Constants
const (
	LambdaVersion = "2.0.0"

	// Output modes
	OutputModeS3   = "s3"
	OutputModeJSON = "json"
	OutputModeCMD  = "cmd"
)
