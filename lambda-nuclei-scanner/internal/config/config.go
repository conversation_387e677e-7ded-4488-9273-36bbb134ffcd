package config

import (
	"os"
	"os/exec"
	"path/filepath"

	"main/internal/types"
)

// SetupLambdaEnvironment configures the Lambda execution environment
func SetupLambdaEnvironment() (*types.Environment, error) {
	env := &types.Environment{
		NucleiBinary:     "/opt/nuclei",
		FileSystem:       "/tmp/",
		NucleiConfigPath: "/opt/nuclei-config.yaml",
		IsLambda:         true,
	}

	env.TargetsFile = env.FileSystem + "targets.txt"
	env.ScanOutput = env.FileSystem + "output.json"

	// Set HOME environment for nuclei
	homeDir := "/tmp"
	if err := os.Setenv("HOME", homeDir); err != nil {
		return nil, err
	}

	// Ensure nuclei config directory exists
	nucleiConfigDirPath := filepath.Join(homeDir, ".config", "nuclei")
	if err := os.Mkdir<PERSON>ll(nucleiConfigDirPath, 0755); err != nil {
		return nil, err
	}

	// Create .nuclei-ignore file if it doesn't exist
	nucleiIgnoreFilePath := filepath.Join(nucleiConfigDirPath, ".nuclei-ignore")
	if _, err := os.Stat(nucleiIgnoreFilePath); os.IsNotExist(err) {
		if file, err := os.Create(nucleiIgnoreFilePath); err != nil {
			return nil, err
		} else {
			file.Close()
		}
	}

	return env, nil
}

// SetupLocalEnvironment configures the local execution environment
func SetupLocalEnvironment() (*types.Environment, error) {
	env := &types.Environment{
		FileSystem:       "./",
		NucleiConfigPath: "",
		IsLambda:         false,
	}

	// For local execution, use nuclei from PATH
	if nucleiPath, err := exec.LookPath("nuclei"); err == nil {
		env.NucleiBinary = nucleiPath
	} else {
		env.NucleiBinary = "nuclei" // Fallback to PATH lookup
	}

	env.TargetsFile = env.FileSystem + "targets.txt"
	env.ScanOutput = env.FileSystem + "output.json"

	// Look for nuclei config in current directory
	if _, err := os.Stat("./nuclei-config.yaml"); err == nil {
		env.NucleiConfigPath = "./nuclei-config.yaml"
	}

	return env, nil
}

// IsLambdaEnvironment checks if we're running in AWS Lambda
func IsLambdaEnvironment() bool {
	return os.Getenv("AWS_LAMBDA_FUNCTION_NAME") != ""
}
