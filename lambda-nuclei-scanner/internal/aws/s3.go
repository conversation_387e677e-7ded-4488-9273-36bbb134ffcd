package aws

import (
	"bufio"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/google/uuid"

	"main/internal/nuclei"
	"main/internal/types"
)

// ProcessS3Output handles S3 upload logic
func ProcessS3Output(result *types.NucleiResult) (string, string) {
	findings, err := nuclei.ParseJSONFindings(result.OutputFile)
	if err != nil {
		return "Error processing findings for S3 upload", fmt.Sprintf("Error processing findings: %v", err)
	}

	if len(findings) == 0 {
		return "No findings to upload to S3", ""
	}

	bucketName := os.Getenv("BUCKET_NAME")
	if bucketName == "" {
		return "S3 upload failed: configuration error", "S3 bucket name not configured"
	}

	s3Path, err := UploadFindings(findings, bucketName)
	if err != nil {
		return "Failed to upload findings to S3", fmt.Sprintf("S3 upload error: %v", err)
	}

	if s3Path == "No findings to upload" {
		return "No findings were uploaded to S3", ""
	}

	log.Printf("Successfully uploaded findings to: %s", s3Path)
	return s3Path, ""
}

// UploadFindings takes in findings, writes them to a file, and uploads to S3
func UploadFindings(findings []interface{}, bucketName string) (string, error) {
	// Get AWS region
	region := os.Getenv("AWS_REGION")
	if region == "" {
		region = "us-east-1" // Default fallback
	}

	// Convert findings to JSON strings
	var s3Findings []string
	for _, finding := range findings {
		jsonFinding, err := json.Marshal(finding)
		if err != nil {
			return "", fmt.Errorf("failed to marshal finding for S3: %w", err)
		}
		s3Findings = append(s3Findings, string(jsonFinding))
	}

	if len(s3Findings) == 0 {
		return "No findings to upload", nil
	}

	// Generate unique filename with timestamp-based partitioning
	t := time.Now()
	uuidVal := uuid.New().String()
	s3Key := fmt.Sprintf("findings/%d/%02d/%02d/%02d/nuclei-findings-%s.json",
		t.Year(), t.Month(), t.Day(), t.Hour(), uuidVal)
	tempFilename := fmt.Sprintf("/tmp/nuclei-findings-s3-%s.json", uuidVal)

	// Write findings to temporary file
	file, err := os.Create(tempFilename)
	if err != nil {
		return "", fmt.Errorf("failed to create temp file for S3 upload %s: %w", tempFilename, err)
	}
	defer os.Remove(tempFilename)
	defer file.Close()

	w := bufio.NewWriter(file)
	for _, finding := range s3Findings {
		if _, err := fmt.Fprintln(w, finding); err != nil {
			return "", fmt.Errorf("failed to write finding to temp file %s: %w", tempFilename, err)
		}
	}
	if err := w.Flush(); err != nil {
		return "", fmt.Errorf("failed to flush temp file %s: %w", tempFilename, err)
	}
	file.Close() // Close file before uploading

	// Create AWS session
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(region),
	})
	if err != nil {
		return "", fmt.Errorf("failed to create AWS session: %w", err)
	}

	// Create S3 uploader
	uploader := s3manager.NewUploader(sess)

	// Reopen file for upload
	findingsFileToUpload, err := os.Open(tempFilename)
	if err != nil {
		return "", fmt.Errorf("failed to open temp file for S3 upload %s: %w", tempFilename, err)
	}
	defer findingsFileToUpload.Close()

	// Upload file to S3
	_, err = uploader.Upload(&s3manager.UploadInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(s3Key),
		Body:   findingsFileToUpload,
	})
	if err != nil {
		return "", fmt.Errorf("failed to upload file %s to S3 bucket %s, key %s: %w",
			tempFilename, bucketName, s3Key, err)
	}

	// Return S3 URI
	s3uri := fmt.Sprintf("s3://%s/%s", bucketName, s3Key)
	log.Printf("Successfully uploaded findings to %s", s3uri)
	return s3uri, nil
}
