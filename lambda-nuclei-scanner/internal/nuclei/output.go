package nuclei

import (
	"bufio"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"

	"main/internal/types"
)

// ProcessOutput processes the nuclei result based on output mode
func ProcessOutput(result *types.NucleiResult, outputMode string) (string, string) {
	switch outputMode {
	case types.OutputModeJSON:
		return ProcessJSONOutput(result)
	default:
		// CMD mode - return base64 encoded output
		return base64.StdEncoding.EncodeToString([]byte(result.Output)), ""
	}
}

// ProcessJSONOutput handles JSON output mode
func ProcessJSONOutput(result *types.NucleiResult) (string, string) {
	if _, err := os.Stat(result.OutputFile); os.IsNotExist(err) {
		return "[]", "" // Empty JSON array if no file
	}

	content, err := os.ReadFile(result.OutputFile)
	if err != nil {
		return "Error reading scan output file", err.Error()
	}

	if len(content) == 0 {
		return "[]", "" // Empty JSON array for empty file
	}

	return string(content), ""
}

// ParseJSONFindings reads the output.json file and returns the findings
func ParseJSONFindings(scanOutputFile string) ([]interface{}, error) {
	// First check if file exists
	stat, err := os.Stat(scanOutputFile)
	if err != nil {
		if os.IsNotExist(err) {
			log.Printf("Output file %s does not exist", scanOutputFile)
			return []interface{}{}, nil // Return empty slice instead of error
		}
		return nil, fmt.Errorf("error accessing file %s: %v", scanOutputFile, err)
	}

	// Check if file is empty
	if stat.Size() == 0 {
		log.Printf("Output file %s is empty (no findings)", scanOutputFile)
		return []interface{}{}, nil // Return empty slice for empty file
	}

	file, err := os.Open(scanOutputFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open file %s: %v", scanOutputFile, err)
	}
	defer file.Close()

	// Create a scanner to read the file line by line
	scanner := bufio.NewScanner(file)

	// Iterate through the file and append the findings to the findings array
	var findings []interface{}
	lineNumber := 0
	for scanner.Scan() {
		lineNumber++
		line := strings.TrimSpace(scanner.Text())

		// Skip empty lines
		if line == "" {
			continue
		}

		var data interface{}
		if err := json.Unmarshal([]byte(line), &data); err != nil {
			// If there is an error, it might be a non-JSON line (e.g. Nuclei summary/error)
			// For now, we log it and skip. Depending on requirements, this could be handled differently.
			log.Printf("Skipping non-JSON line %d in %s: %v (line: %s)", lineNumber, scanOutputFile, err, line)
			continue
		}
		findings = append(findings, data)
	}

	// Check for errors while reading the file
	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading file %s: %v", scanOutputFile, err)
	}

	log.Printf("Successfully parsed %d findings from %s", len(findings), scanOutputFile)
	return findings, nil
}
