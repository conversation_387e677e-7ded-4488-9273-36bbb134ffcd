package nuclei

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"main/internal/types"
)

// ValidateBinary checks if nuclei binary and config are available
func ValidateBinary(env *types.Environment) error {
	// Check nuclei binary
	if stat, err := os.Stat(env.NucleiBinary); err != nil {
		return fmt.Errorf("nuclei binary not found at %s: %v", env.NucleiBinary, err)
	} else {
		log.Printf("Nuclei binary validated: %s (%d bytes)", env.NucleiBinary, stat.Size())
	}

	// Check config file if specified
	if env.NucleiConfigPath != "" {
		if _, err := os.Stat(env.NucleiConfigPath); err != nil {
			return fmt.Errorf("nuclei config not found at %s: %v", env.NucleiConfigPath, err)
		} else {
			log.Printf("Nuclei config validated: %s", env.NucleiConfigPath)
		}
	}

	return nil
}

// DetectResults analyzes nuclei output to determine if results were found
func DetectResults(output string) bool {
	lowerOutput := strings.ToLower(output)

	// First check for command line errors - these should NOT be considered as "found results"
	if strings.Contains(lowerOutput, "flag provided but not defined") ||
		strings.Contains(lowerOutput, "error parsing") ||
		strings.Contains(lowerOutput, "invalid flag") ||
		strings.Contains(lowerOutput, "unknown flag") {
		log.Printf("Detected nuclei command line error, not scan results")
		return false
	}

	// Check for explicit "no results" messages first (these take priority)
	if strings.Contains(lowerOutput, "no results found") ||
		strings.Contains(lowerOutput, "better luck next time") ||
		strings.Contains(lowerOutput, "no results") {
		log.Printf("Nuclei explicitly reported no results found")
		return false
	}

	// Check for actual scan results indicators
	hasPositiveResults := strings.Contains(lowerOutput, "results found") ||
		strings.Contains(lowerOutput, "vulnerabilities detected") ||
		strings.Contains(lowerOutput, "matched") ||
		strings.Contains(lowerOutput, "identified")

	if hasPositiveResults {
		log.Printf("Nuclei output indicates positive results found")
		return true
	}

	// Default to false if we can't definitively determine results
	log.Printf("Could not determine results from nuclei output, assuming no results")
	return false
}

// ValidateOutputFile checks if the expected output file exists and has appropriate content
func ValidateOutputFile(filePath string, expectResults bool) error {
	stat, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		if expectResults {
			// Enhanced debugging when nuclei finds results but file is missing
			log.Printf("CRITICAL: Output file %s not created despite finding results", filePath)

			// Check what files nuclei might have created
			if files, dirErr := os.ReadDir("/tmp"); dirErr == nil {
				log.Printf("Files in /tmp after nuclei execution:")
				for _, file := range files {
					if strings.Contains(file.Name(), "nuclei") ||
						strings.Contains(file.Name(), "output") ||
						strings.HasSuffix(file.Name(), ".json") {
						if info, statErr := file.Info(); statErr == nil {
							log.Printf("  - %s (%d bytes)", file.Name(), info.Size())
						}
					}
				}
			}

			// Check current working directory as well
			if pwd, pwdErr := os.Getwd(); pwdErr == nil {
				log.Printf("Current working directory: %s", pwd)
				if files, dirErr := os.ReadDir(pwd); dirErr == nil {
					log.Printf("Files in current directory:")
					for _, file := range files {
						if strings.HasSuffix(file.Name(), ".json") {
							if info, statErr := file.Info(); statErr == nil {
								log.Printf("  - %s (%d bytes)", file.Name(), info.Size())
							}
						}
					}
				}
			}

			return fmt.Errorf("nuclei found results but output file was not created")
		}
		// Create empty file if no results expected
		if file, createErr := os.Create(filePath); createErr == nil {
			file.Close()
			log.Printf("Created empty output file: %s", filePath)
		}
		return nil
	}
	if err != nil {
		return fmt.Errorf("error accessing output file: %v", err)
	}

	if stat.Size() == 0 && expectResults {
		return fmt.Errorf("output file exists but is empty despite finding results")
	}

	log.Printf("Output file validated: %s (%d bytes)", filePath, stat.Size())
	return nil
}

// ValidateWithDelay validates output file with additional checks for Lambda environment
func ValidateWithDelay(env *types.Environment, outputMode string, hasResults bool) error {
	if outputMode == types.OutputModeS3 || outputMode == types.OutputModeJSON {
		// Small delay to ensure file writing is complete
		time.Sleep(100 * time.Millisecond)

		// Additional check: try to find any files nuclei might have created with different names
		if hasResults && env.IsLambda {
			log.Printf("Nuclei indicated results found, checking for any output files...")
			if files, dirErr := os.ReadDir("/tmp"); dirErr == nil {
				for _, file := range files {
					fileName := file.Name()
					if (strings.Contains(fileName, "nuclei") || strings.Contains(fileName, "output")) &&
						strings.HasSuffix(fileName, ".json") {
						if info, statErr := file.Info(); statErr == nil {
							log.Printf("Found potential nuclei output file: %s (%d bytes)", fileName, info.Size())
						}
					}
				}
			}
		}

		return ValidateOutputFile(env.ScanOutput, hasResults)
	}
	return nil
}

// TestWritePermissions tests write permissions to the output directory
func TestWritePermissions(env *types.Environment) error {
	testFile := filepath.Dir(env.ScanOutput) + "/write_test.tmp"
	if testFd, err := os.Create(testFile); err != nil {
		log.Printf("ERROR: Cannot write to output directory %s: %v", filepath.Dir(env.ScanOutput), err)
		return fmt.Errorf("cannot write to output directory: %v", err)
	} else {
		testFd.Close()
		os.Remove(testFile)
		log.Printf("✓ Write permissions confirmed for output directory")
	}

	// Log absolute path being used
	if absPath, err := filepath.Abs(env.ScanOutput); err == nil {
		log.Printf("Nuclei will write to absolute path: %s", absPath)
	}

	return nil
}
