package nuclei

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"os/exec"
	"strings"

	"main/internal/types"
)

// PrepareCommand builds the nuclei command arguments for Lambda execution
func PrepareCommand(env *types.Environment, request *types.ScanRequest) ([]string, error) {
	// Base arguments
	cmdArgs := []string{"-duc"} // Disable template updates and checks

	// Add config file if available
	if env.NucleiConfigPath != "" {
		if _, err := os.Stat(env.NucleiConfigPath); err == nil {
			cmdArgs = append(cmdArgs, "-config", env.NucleiConfigPath)
		}
	}

	// Add user arguments
	if len(request.Args) > 0 {
		cmdArgs = append(cmdArgs, request.Args...)
	}

	// Handle targets
	if len(request.Targets) == 0 {
		return nil, fmt.Errorf("no targets provided")
	}

	if len(request.Targets) == 1 {
		cmdArgs = append(cmdArgs, "-u", request.Targets[0])
	} else {
		if err := WriteTargetsToFile(request.Targets, env.TargetsFile); err != nil {
			return nil, fmt.Errorf("failed to write targets: %v", err)
		}
		cmdArgs = append(cmdArgs, "-l", env.TargetsFile)
	}

	// Handle output for Lambda modes
	if request.Output == types.OutputModeS3 || request.Output == types.OutputModeJSON {
		cmdArgs = append(cmdArgs, "-o", env.ScanOutput)

		// Check if user already provided -json flag
		hasJsonFlag := false
		for _, arg := range request.Args {
			if arg == "-json" || arg == "-j" {
				hasJsonFlag = true
				log.Printf("User provided -json flag, keeping it")
				break
			}
		}
		if !hasJsonFlag {
			// Don't add -json automatically for Nuclei v3.1.7 as it's not supported
			// The .json extension in output file should be sufficient for JSON output
			log.Printf("JSON output will be determined by .json file extension (no -json flag needed)")
		}
		cmdArgs = append(cmdArgs, "-no-color")
	}

	return cmdArgs, nil
}

// PrepareCommandForCLI builds the nuclei command arguments for CLI execution
func PrepareCommandForCLI(env *types.Environment, targets []string, nucleiArgs []string, outputFile string) ([]string, error) {
	// Base arguments for Nuclei
	cmdArgs := []string{"-duc"}

	// Add config file if available
	if env.NucleiConfigPath != "" {
		if _, err := os.Stat(env.NucleiConfigPath); err == nil {
			cmdArgs = append(cmdArgs, "-config", env.NucleiConfigPath)
		}
	}

	// Add user arguments
	if len(nucleiArgs) > 0 {
		cmdArgs = append(cmdArgs, nucleiArgs...)
	}

	// Handle targets
	if len(targets) == 0 {
		return nil, fmt.Errorf("no targets provided")
	}

	if len(targets) == 1 {
		cmdArgs = append(cmdArgs, "-u", targets[0])
	} else {
		err := WriteTargetsToFile(targets, env.TargetsFile)
		if err != nil {
			return nil, fmt.Errorf("failed to write targets to file: %v", err)
		}
		cmdArgs = append(cmdArgs, "-l", env.TargetsFile)
	}

	// Handle output
	if outputFile != "stdout" {
		cmdArgs = append(cmdArgs, "-o", outputFile)
		// Add -no-color to prevent ANSI codes from interfering with JSON output
		cmdArgs = append(cmdArgs, "-no-color")
		os.Remove(outputFile) // Clean up any existing file
	}

	return cmdArgs, nil
}

// Execute runs the Nuclei binary with the given arguments
func Execute(env *types.Environment, args []string) (string, error) {
	log.Printf("Executing nuclei with args: %v", args)

	// Check if nuclei binary exists and is executable
	if _, err := os.Stat(env.NucleiBinary); err != nil {
		return "", fmt.Errorf("nuclei binary not found at %s: %v", env.NucleiBinary, err)
	}

	cmd := exec.Command(env.NucleiBinary, args...)

	// Set working directory appropriately
	if env.IsLambda {
		cmd.Dir = "/tmp"
	}

	// Capture both stdout and stderr
	output, err := cmd.CombinedOutput()
	outputStr := string(output)

	if err != nil {
		// Check if it's an exit code error
		if exitError, ok := err.(*exec.ExitError); ok {
			log.Printf("Nuclei exited with code %d", exitError.ExitCode())
			// Sometimes nuclei returns non-zero exit code even when scan completes successfully
			// Check if the output contains actual error messages vs just scan results
			if strings.Contains(outputStr, "Error") || strings.Contains(outputStr, "FATAL") || strings.Contains(outputStr, "panic") {
				return outputStr, fmt.Errorf("nuclei execution failed with exit code %d: %v", exitError.ExitCode(), err)
			} else {
				log.Printf("Nuclei returned non-zero exit code but output seems normal, treating as success")
				return outputStr, nil
			}
		}
		return outputStr, fmt.Errorf("nuclei execution failed: %v", err)
	}

	log.Printf("Nuclei execution completed successfully")
	return outputStr, nil
}

// ExecuteWithValidation runs nuclei and validates the results
func ExecuteWithValidation(env *types.Environment, cmdArgs []string, outputMode string) (*types.NucleiResult, error) {
	log.Printf("Executing nuclei with args: %v", cmdArgs)

	// Clean up any existing output file and test permissions for file output modes
	if outputMode == types.OutputModeS3 || outputMode == types.OutputModeJSON {
		os.Remove(env.ScanOutput)
		if err := TestWritePermissions(env); err != nil {
			return &types.NucleiResult{Error: err}, err
		}
	}

	// Execute nuclei
	output, err := Execute(env, cmdArgs)
	if err != nil {
		return &types.NucleiResult{
			Output: output,
			Error:  fmt.Errorf("nuclei execution failed: %v", err),
		}, err
	}

	// Analyze results
	hasResults := DetectResults(output)
	log.Printf("Nuclei execution completed. Detected results: %v", hasResults)
	log.Printf("Nuclei output: %s", output)

	// Validate output file for JSON modes
	if err := ValidateWithDelay(env, outputMode, hasResults); err != nil {
		return &types.NucleiResult{
			Output:     output,
			HasResults: hasResults,
			OutputFile: env.ScanOutput,
			Error:      err,
		}, err
	}

	return &types.NucleiResult{
		Output:     output,
		HasResults: hasResults,
		OutputFile: env.ScanOutput,
	}, nil
}

// WriteTargetsToFile writes a list of targets to a specified file, one target per line
func WriteTargetsToFile(targets []string, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("failed to create targets file %s: %w", filename, err)
	}
	defer file.Close()

	w := bufio.NewWriter(file)
	for _, target := range targets {
		if _, err := fmt.Fprintln(w, target); err != nil {
			return fmt.Errorf("failed to write target '%s' to file %s: %w", target, filename, err)
		}
	}
	return w.Flush() // Ensure all buffered data is written to the file
}

// ReadTargetsFromFile reads targets from a file, one per line
func ReadTargetsFromFile(filename string) ([]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var targets []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		target := strings.TrimSpace(scanner.Text())
		if target != "" && !strings.HasPrefix(target, "#") {
			targets = append(targets, target)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return targets, nil
}
