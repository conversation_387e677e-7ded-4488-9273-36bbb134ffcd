package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"strings"

	"main/internal/config"
	"main/internal/nuclei"
	"main/internal/types"
)

// parseCLIArgs parses command line arguments for standalone execution
func parseCLIArgs() (*types.CLIConfig, error) {
	config := &types.CLIConfig{}

	flag.StringVar(&config.Targets, "targets", "", "Comma-separated list of targets to scan")
	flag.StringVar(&config.TargetFile, "target-file", "", "File containing targets to scan (one per line)")
	flag.StringVar(&config.Args, "args", "", "Additional nuclei arguments (e.g., '-t dns -severity high')")
	flag.StringVar(&config.OutputFile, "output-file", "", "Output file for JSON results (optional, defaults to stdout)")

	flag.Parse()

	// Validate that either targets or target-file is provided
	if config.Targets == "" && config.TargetFile == "" {
		return nil, fmt.Errorf("either -targets or -target-file must be specified")
	}

	if config.Targets != "" && config.TargetFile != "" {
		return nil, fmt.Errorf("cannot specify both -targets and -target-file")
	}

	return config, nil
}

// executeNucleiScan runs nuclei with the given parameters and returns the output
func executeNucleiScan(env *types.Environment, targets []string, nucleiArgs []string, outputFile string) (string, error) {
	// Update environment output file for custom output
	if outputFile != "stdout" {
		env.ScanOutput = outputFile
	}

	// Prepare command
	cmdArgs, err := nuclei.PrepareCommandForCLI(env, targets, nucleiArgs, outputFile)
	if err != nil {
		return "", err
	}

	// Execute nuclei with validation
	result, err := nuclei.ExecuteWithValidation(env, cmdArgs, outputFile)
	if err != nil {
		return result.Output, err
	}

	// Return file contents for file output, or command output for stdout
	if outputFile != "stdout" {
		if content, readErr := os.ReadFile(outputFile); readErr == nil {
			return string(content), nil
		}
	}

	return result.Output, nil
}

// runCLI executes nuclei in CLI mode
func runCLI() error {
	// Setup local environment
	env, err := config.SetupLocalEnvironment()
	if err != nil {
		return fmt.Errorf("failed to setup local environment: %v", err)
	}

	// Parse CLI arguments
	cliConfig, err := parseCLIArgs()
	if err != nil {
		return fmt.Errorf("CLI argument error: %v", err)
	}

	// Prepare targets
	var targets []string
	if cliConfig.Targets != "" {
		targets = strings.Split(cliConfig.Targets, ",")
		// Trim whitespace from each target
		for i, target := range targets {
			targets[i] = strings.TrimSpace(target)
		}
	} else if cliConfig.TargetFile != "" {
		targets, err = nuclei.ReadTargetsFromFile(cliConfig.TargetFile)
		if err != nil {
			return fmt.Errorf("failed to read targets from file: %v", err)
		}
	}

	// Parse additional nuclei arguments
	var nucleiArgs []string
	if cliConfig.Args != "" {
		nucleiArgs = strings.Fields(cliConfig.Args)
	}

	// Set output file
	outputFile := cliConfig.OutputFile
	if outputFile == "" {
		outputFile = "stdout"
	}

	// Execute nuclei scan
	result, err := executeNucleiScan(env, targets, nucleiArgs, outputFile)
	if err != nil {
		return fmt.Errorf("nuclei scan failed: %v", err)
	}

	// Handle output
	if outputFile == "stdout" {
		fmt.Print(result)
	} else {
		log.Printf("Results written to: %s", outputFile)
	}

	// Cleanup
	if len(targets) > 1 {
		os.Remove(env.TargetsFile)
	}

	return nil
}

func main() {
	if err := runCLI(); err != nil {
		log.Fatalf("Error: %v", err)
	}
}
