package main

import (
	"context"

	"github.com/aws/aws-lambda-go/lambda"

	"main/internal/types"
	"main/pkg/scanner"
)

// handler is the AWS Lambda function handler
func handler(ctx context.Context, event types.ScanRequest) (types.ScanResponse, error) {
	// Create scanner instance
	sc, err := scanner.NewScanner()
	if err != nil {
		return types.ScanResponse{
			Error: err.Error(),
		}, err
	}

	// Execute scan
	response, err := sc.Scan(&event)
	if err != nil {
		return *response, err
	}

	return *response, nil
}

func main() {
	lambda.Start(handler)
}
