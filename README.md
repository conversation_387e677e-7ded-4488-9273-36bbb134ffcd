# FastScan

## Project Overview

- **Goal:** A security vulnerability scanning platform that leverages Nuclei scanner to identify security vulnerabilities, misconfigurations, and exposures in web applications and infrastructure.
- **High-Performance Backend (Nuclear Pond):** Offers flexible Nuclei execution modes (local parallel and AWS Lambda) and an HTTP API for programmatic control.
- **Serverless Scanner (Nuclei Lambda):** A dedicated AWS Lambda function for scalable, serverless Nuclei execution, supporting multiple output formats and S3 integration.
- **Monorepo Tooling:** Standard package management with npm/yarn
- **Primary Technologies (Overall):**
  - Frontend: React, TypeScript, Vite, TailwindCSS, TanStack
  - Backend: Go (Nuclear Pond)
  - Infrastructure: AWS Lambda, S3, DynamoDB, Glue, Terraform
  - Scanner: Nuclei

## Monorepo Root Structure

- `/frontend/`: React-based web application for the user interface
- `/nuclear_pond/`: Go-based implementation of Nuclear Pond, the backend service for scanning orchestration, API, and execution management.
- `/terraform/`: Terraform configuration for deploying the entire FastScan infrastructure on AWS.
- `/lambda-nuclei-scanner/`: AWS Lambda function specifically designed for running Nuclei scans in a serverless environment.

## Documentation

This project is composed of several key components, each with its own detailed documentation:

- **Frontend:**
  - [Frontend Architecture](frontend/docs/frontend-architecture.md)
  - [Frontend Deployment](frontend/docs/frontend-deployment.md)

- **Nuclear Pond (Backend):**
  - [Getting Started Guide](nuclear_pond/docs/getting-started.md)
  - [User Guide](nuclear_pond/docs/user-guide.md)
  - [API Reference](nuclear_pond/docs/api-reference.md)
  - [Architecture Overview](nuclear_pond/docs/architecture.md)
  - [Deployment Guide (AWS)](terraform/nuclear_pond_backend/README.md)

- **Nuclei Lambda Scanner:**
  - [Overview and Features](lambda-nuclei-scanner/README.md)
  - [Deployment Guide (AWS Lambda)](terraform/nuclei_lambda/README.md)