# ==============================================================================
# FRONTEND DEPLOYMENT AUTOMATION
# ==============================================================================

# Generate environment-specific configuration for the frontend
# This creates .env files that Vite uses during the build process
resource "local_file" "frontend_env" {
  content = <<-EOT
# Frontend Environment Configuration for ${var.environment}
# Generated automatically by Terraform - do not edit manually

# Backend API Configuration
VITE_API_URL=${var.api_url}
VITE_API_KEY=${var.api_key}

# Application Configuration  
VITE_DEMO_PASSWORD=${var.demo_password}
VITE_ENVIRONMENT=${var.environment}

# Frontend Deployment Information (for reference)
# S3 Bucket: ${aws_s3_bucket.frontend.id}
# CloudFront Enabled: ${var.enable_cloudfront}
${var.enable_cloudfront ? "# CloudFront Distribution: ${aws_cloudfront_distribution.frontend[0].domain_name}" : "# S3 Website Endpoint: ${aws_s3_bucket_website_configuration.frontend.website_endpoint}"}
EOT

  filename = "${path.module}/../../frontend/.env.${var.environment}"
  
  # Set appropriate permissions for environment files
  file_permission = "0644"
}

# Create deployment script for the specific environment
# This script automates the build and deployment process
resource "local_file" "deploy_script" {
  content = <<-EOT
#!/bin/bash
# Frontend Deployment Script for ${var.environment} Environment
# Generated automatically by Terraform

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT="${var.environment}"
S3_BUCKET="${aws_s3_bucket.frontend.id}"
CLOUDFRONT_ENABLED="${var.enable_cloudfront}"
${var.enable_cloudfront ? "CLOUDFRONT_DISTRIBUTION_ID=\"${aws_cloudfront_distribution.frontend[0].id}\"" : "CLOUDFRONT_DISTRIBUTION_ID=\"\""}

echo -e "$${BLUE}=== Frontend Deployment Script for $${ENVIRONMENT} ===$${NC}"
echo -e "$${BLUE}CloudFront Enabled: $${CLOUDFRONT_ENABLED}$${NC}"
echo -e "$${BLUE}S3 Bucket: $${S3_BUCKET}$${NC}"
echo ""

# Check dependencies
echo -e "$${YELLOW}Checking dependencies...$${NC}"
if ! command -v aws &> /dev/null; then
    echo -e "$${RED}Error: AWS CLI is not installed or not in PATH$${NC}"
    exit 1
fi

if ! command -v yarn &> /dev/null; then
    echo -e "$${RED}Error: Yarn is not installed or not in PATH$${NC}"
    exit 1
fi

# Navigate to frontend directory
echo -e "$${YELLOW}Navigating to frontend directory...$${NC}"
cd ../../frontend

# Check if environment file exists
if [ ! -f ".env.$${ENVIRONMENT}" ]; then
    echo -e "$${RED}Error: Environment file .env.$${ENVIRONMENT} not found$${NC}"
    echo "Please run 'terraform apply' to generate the environment file"
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo -e "$${YELLOW}Installing dependencies...$${NC}"
    yarn install
fi

# Build the frontend
echo -e "$${YELLOW}Building frontend for $${ENVIRONMENT} environment...$${NC}"
yarn build:$${ENVIRONMENT}

# Check if build was successful
if [ ! -d "dist" ]; then
    echo -e "$${RED}Error: Build failed - dist directory not found$${NC}"
    exit 1
fi

# Deploy to S3
echo -e "$${YELLOW}Deploying to S3 bucket: $${S3_BUCKET}...$${NC}"
aws s3 sync dist/ s3://$${S3_BUCKET} --delete

# Check if S3 sync was successful
if [ $? -eq 0 ]; then
    echo -e "$${GREEN}✓ Successfully deployed to S3$${NC}"
else
    echo -e "$${RED}Error: Failed to deploy to S3$${NC}"
    exit 1
fi

# Invalidate CloudFront cache if CloudFront is enabled
if [ "$${CLOUDFRONT_ENABLED}" = "true" ]; then
    echo -e "$${YELLOW}Invalidating CloudFront cache for distribution: $${CLOUDFRONT_DISTRIBUTION_ID}...$${NC}"
    aws cloudfront create-invalidation --distribution-id $${CLOUDFRONT_DISTRIBUTION_ID} --paths "/*" > /dev/null
    
    if [ $? -eq 0 ]; then
        echo -e "$${GREEN}✓ CloudFront cache invalidation started$${NC}"
        echo -e "$${YELLOW}Note: Cache invalidation may take 5-15 minutes to complete$${NC}"
    else
        echo -e "$${RED}Warning: Failed to invalidate CloudFront cache$${NC}"
        echo -e "$${YELLOW}You may need to wait for cache to expire or invalidate manually$${NC}"
    fi
else
    echo -e "$${YELLOW}CloudFront is not enabled, skipping cache invalidation...$${NC}"
fi

echo ""
echo -e "$${GREEN}=== Deployment Complete! ===$${NC}"
echo -e "$${GREEN}Frontend URL: ${var.enable_cloudfront ? (var.create_route53_record && var.frontend_domain_name != "" ? "https://${var.frontend_domain_name}" : "https://${aws_cloudfront_distribution.frontend[0].domain_name}") : "http://${aws_s3_bucket_website_configuration.frontend.website_endpoint}"}$${NC}"

# Show additional URLs for reference
echo ""
echo -e "$${BLUE}=== Available URLs ===$${NC}"
echo -e "$${BLUE}S3 Direct: http://${aws_s3_bucket_website_configuration.frontend.website_endpoint}$${NC}"
${var.enable_cloudfront ? "echo -e \"$${BLUE}CloudFront: https://${aws_cloudfront_distribution.frontend[0].domain_name}$${NC}\"" : ""}
${var.create_route53_record && var.frontend_domain_name != "" ? "echo -e \"$${BLUE}Custom Domain: https://${var.frontend_domain_name}$${NC}\"" : ""}
EOT

  filename        = "${path.module}/deploy-frontend-${var.environment}.sh"
  file_permission = "0755"
}

# Create a quick deployment help file
resource "local_file" "deployment_help" {
  content = <<-EOT
# Frontend Deployment Guide for ${var.environment} Environment

## Quick Start

1. **Deploy Infrastructure:**
   ```bash
   cd terraform
   terraform apply -target=module.frontend
   ```

2. **Deploy Frontend Code:**
   ```bash
   cd terraform/frontend
   ./deploy-frontend-${var.environment}.sh
   ```

## Manual Deployment

If you prefer manual deployment:

1. **Build the frontend:**
   ```bash
   cd frontend
   yarn build:${var.environment}
   ```

2. **Deploy to S3:**
   ```bash
   aws s3 sync dist/ s3://${aws_s3_bucket.frontend.id} --delete
   ```

3. **Invalidate CloudFront cache (if CloudFront enabled):**
   ```bash
   ${var.enable_cloudfront ? "aws cloudfront create-invalidation --distribution-id ${aws_cloudfront_distribution.frontend[0].id} --paths \"/*\"" : "# CloudFront not enabled - no cache invalidation needed"}
   ```

## Configuration

- **Environment:** ${var.environment}
- **CloudFront:** ${var.enable_cloudfront ? "Enabled" : "Disabled"}
- **S3 Bucket:** ${aws_s3_bucket.frontend.id}
- **Frontend URL:** ${var.enable_cloudfront ? (var.create_route53_record && var.frontend_domain_name != "" ? "https://${var.frontend_domain_name}" : "https://${aws_cloudfront_distribution.frontend[0].domain_name}") : "http://${aws_s3_bucket_website_configuration.frontend.website_endpoint}"}

## Environment Variables

The following environment variables are automatically configured:

- `VITE_API_URL`: ${var.api_url}
- `VITE_ENVIRONMENT`: ${var.environment}
- `VITE_API_KEY`: [SENSITIVE]
- `VITE_DEMO_PASSWORD`: [SENSITIVE]

## Troubleshooting

### Build Issues
- Ensure you have Node.js and Yarn installed
- Run `yarn install` to install dependencies
- Check the `.env.${var.environment}` file exists

### Deployment Issues
- Verify AWS CLI is configured with appropriate permissions
- Check S3 bucket exists: `aws s3 ls s3://${aws_s3_bucket.frontend.id}`
- Verify CloudFront distribution (if enabled): `aws cloudfront get-distribution --id ${var.enable_cloudfront ? aws_cloudfront_distribution.frontend[0].id : "N/A"}`

### Access Issues
- S3 direct access: http://${aws_s3_bucket_website_configuration.frontend.website_endpoint}
${var.enable_cloudfront ? "- CloudFront access: https://${aws_cloudfront_distribution.frontend[0].domain_name}" : ""}
${var.create_route53_record && var.frontend_domain_name != "" ? "- Custom domain: https://${var.frontend_domain_name}" : ""}
EOT

  filename = "${path.module}/DEPLOYMENT-${upper(var.environment)}.md"
  file_permission = "0644"
}
