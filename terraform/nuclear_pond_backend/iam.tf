# ==============================================================================
# IAM ROLES AND POLICIES FOR NUCLEAR POND BACKEND
# ==============================================================================

# IAM Role for ECS Task Execution (pulls images, writes logs)
resource "aws_iam_role" "ecs_task_execution_role" {
  name = "${var.project_name}-${var.environment}-ecs-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-ecs-task-execution-role"
    Environment = var.environment
    Component   = "backend"
  })
}

# Attach AWS managed policy for ECS task execution
resource "aws_iam_role_policy_attachment" "ecs_task_execution_role_policy" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# Additional policy for ECR access (if needed for private repositories)
resource "aws_iam_role_policy" "ecs_task_execution_ecr_policy" {
  name = "${var.project_name}-${var.environment}-ecs-task-execution-ecr-policy"
  role = aws_iam_role.ecs_task_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecr:GetAuthorizationToken",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage"
        ]
        Resource = "*"
      }
    ]
  })
}

# IAM Role for the Nuclear Pond Application (ECS Task Role)
resource "aws_iam_role" "nuclear_pond_task_role" {
  name = "${var.project_name}-${var.environment}-nuclear-pond-task-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-nuclear-pond-task-role"
    Environment = var.environment
    Component   = "backend"
  })
}

# Policy for Nuclear Pond application to invoke Lambda and access DynamoDB
resource "aws_iam_policy" "nuclear_pond_task_policy" {
  name        = "${var.project_name}-${var.environment}-nuclear-pond-task-policy"
  description = "Policy for Nuclear Pond ECS tasks to invoke Lambda and access DynamoDB"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowLambdaInvoke"
        Effect = "Allow"
        Action = [
          "lambda:InvokeFunction",
          "lambda:InvokeAsync"
        ]
        Resource = [
          var.lambda_function_arn,
          "${var.lambda_function_arn}:*"
        ]
      },
      {
        Sid    = "AllowDynamoDBAccess"
        Effect = "Allow"
        Action = [
          "dynamodb:GetItem",
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem",
          "dynamodb:Query",
          "dynamodb:Scan"
        ]
        Resource = var.dynamodb_table_arn
      },
      {
        Sid    = "AllowCloudWatchLogs"
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Sid    = "AllowS3TargetsAccess"
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject"
        ]
        Resource = "${aws_s3_bucket.nuclear_pond_targets.arn}/scan-targets/*"
      },
      {
        Sid    = "AllowS3TargetsBucketList"
        Effect = "Allow"
        Action = [
          "s3:ListBucket"
        ]
        Resource = aws_s3_bucket.nuclear_pond_targets.arn
        Condition = {
          StringLike = {
            "s3:prefix" = "scan-targets/*"
          }
        }
      }
    ]
  })

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-nuclear-pond-task-policy"
    Environment = var.environment
    Component   = "backend"
  })
}

# Attach the custom policy to the task role
resource "aws_iam_role_policy_attachment" "nuclear_pond_task_role_policy_attachment" {
  role       = aws_iam_role.nuclear_pond_task_role.name
  policy_arn = aws_iam_policy.nuclear_pond_task_policy.arn
}
