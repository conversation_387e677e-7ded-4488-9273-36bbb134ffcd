# ==============================================================================
# ECS CLUSTER AND SERVICE FOR NUCLEAR POND BACKEND
# ==============================================================================

# ECS Cluster
resource "aws_ecs_cluster" "nuclear_pond_cluster" {
  name = "${var.project_name}-${var.environment}-cluster"

  # Enable container insights for monitoring
  setting {
    name  = "containerInsights"
    value = "enabled"
  }

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-cluster"
    Environment = var.environment
    Component   = "backend"
  })
}

# ECS Cluster Capacity Providers (for cost optimization)
resource "aws_ecs_cluster_capacity_providers" "nuclear_pond_cluster_cp" {
  cluster_name = aws_ecs_cluster.nuclear_pond_cluster.name

  capacity_providers = ["FARGATE", "FARGATE_SPOT"]

  default_capacity_provider_strategy {
    base              = 1
    weight            = 100
    capacity_provider = "FARGATE"
  }
}

# ECS Task Definition
resource "aws_ecs_task_definition" "nuclear_pond_task_def" {
  family                   = "${var.project_name}-${var.environment}-nuclearpond-service"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.task_cpu
  memory                   = var.task_memory
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  task_role_arn            = aws_iam_role.nuclear_pond_task_role.arn

  container_definitions = jsonencode([
    {
      name      = "nuclearpond-app"
      image     = "${aws_ecr_repository.nuclear_pond_repo.repository_url}:latest"
      essential = true

      portMappings = [
        {
          containerPort = var.container_port
          hostPort      = var.container_port
          protocol      = "tcp"
        }
      ]

      environment = [
        {
          name  = "NUCLEARPOND_API_KEY"
          value = var.api_key
        },
        {
          name  = "AWS_REGION"
          value = data.aws_region.current.name
        },
        {
          name  = "AWS_LAMBDA_FUNCTION_NAME"
          value = var.lambda_function_name
        },
        {
          name  = "AWS_DYNAMODB_TABLE"
          value = var.dynamodb_table_name
        },
        {
          name  = "AWS_S3_BUCKET"
          value = aws_s3_bucket.nuclear_pond_targets.bucket
        },
        {
          name  = "ENVIRONMENT"
          value = var.environment
        },
        {
          name  = "PORT"
          value = tostring(var.container_port)
        }
      ]

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.ecs_nuclear_pond_lg.name
          "awslogs-region"        = data.aws_region.current.name
          "awslogs-stream-prefix" = "ecs"
        }
      }

      # Health check
      healthCheck = {
        command = [
          "CMD-SHELL",
          "curl -f http://localhost:${var.container_port}${var.health_check_path} || exit 1"
        ]
        interval    = 30
        timeout     = 5
        retries     = 3
        startPeriod = 60
      }

      # Resource limits
      memoryReservation = tonumber(var.task_memory) / 2

      # Stop timeout
      stopTimeout = 30
    }
  ])

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-task-definition"
    Environment = var.environment
    Component   = "backend"
  })
}

# ECS Service
resource "aws_ecs_service" "nuclear_pond_service" {
  name            = "${var.project_name}-${var.environment}-nuclearpond-service"
  cluster         = aws_ecs_cluster.nuclear_pond_cluster.id
  task_definition = aws_ecs_task_definition.nuclear_pond_task_def.arn
  desired_count   = var.desired_count
  launch_type     = "FARGATE"

  # Platform version for Fargate
  platform_version = "LATEST"

  # Network configuration
  network_configuration {
    subnets          = var.private_subnet_ids
    security_groups  = [aws_security_group.ecs_service_sg.id]
    assign_public_ip = false
  }

  # Load balancer configuration
  load_balancer {
    target_group_arn = aws_lb_target_group.nuclear_pond_tg.arn
    container_name   = "nuclearpond-app"
    container_port   = var.container_port
  }

  # Deployment configuration
  deployment_maximum_percent         = 200
  deployment_minimum_healthy_percent = 100

  deployment_circuit_breaker {
    enable   = true
    rollback = true
  }

  # Service discovery (optional)
  # service_registries {
  #   registry_arn = aws_service_discovery_service.nuclear_pond.arn
  # }

  # Auto-scaling (optional)
  # enable_execute_command = true  # For debugging with ECS Exec

  # Ensure the load balancer is created before the service
  depends_on = [aws_lb_listener.http_listener]

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-service"
    Environment = var.environment
    Component   = "backend"
  })

  lifecycle {
    ignore_changes = [desired_count]
  }
}
