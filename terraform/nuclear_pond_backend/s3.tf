# ==============================================================================
# S3 BUCKET FOR NUCLEAR POND BACKEND
# ==============================================================================

# Local values for S3 bucket naming
locals {
  s3_bucket_name = var.s3_bucket_name != null ? var.s3_bucket_name : "${var.project_name}-${var.environment}-nuclear-pond-targets-${random_id.bucket_suffix.hex}"
}

# Random ID for bucket name uniqueness
resource "random_id" "bucket_suffix" {
  byte_length = 4
}

# ==============================================================================
# MAIN S3 BUCKET
# ==============================================================================

# S3 bucket for storing scan targets
#tfsec:ignore:aws-s3-enable-bucket-logging tfsec:ignore:aws-s3-enable-versioning
resource "aws_s3_bucket" "nuclear_pond_targets" {
  bucket = local.s3_bucket_name

  # Allow Terraform to delete the bucket and all objects during destroy
  force_destroy = var.s3_force_destroy

  tags = merge(var.tags, {
    Name        = local.s3_bucket_name
    Component   = "nuclear-pond-backend"
    Environment = var.environment
    Purpose     = "scan-targets-storage"
  })
}

# ==============================================================================
# BUCKET SECURITY CONFIGURATION
# ==============================================================================

# Server-side encryption configuration
#tfsec:ignore:aws-s3-encryption-customer-key
resource "aws_s3_bucket_server_side_encryption_configuration" "nuclear_pond_targets_encryption" {
  bucket = aws_s3_bucket.nuclear_pond_targets.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }

    bucket_key_enabled = true
  }
}

# Block public access
resource "aws_s3_bucket_public_access_block" "nuclear_pond_targets_pab" {
  bucket = aws_s3_bucket.nuclear_pond_targets.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Bucket versioning (disabled for cost optimization)
resource "aws_s3_bucket_versioning" "nuclear_pond_targets_versioning" {
  bucket = aws_s3_bucket.nuclear_pond_targets.id

  versioning_configuration {
    status = "Disabled"
  }
}

# ==============================================================================
# LIFECYCLE CONFIGURATION
# ==============================================================================

# Lifecycle configuration to automatically clean up old scan targets
resource "aws_s3_bucket_lifecycle_configuration" "nuclear_pond_targets_lifecycle" {
  bucket = aws_s3_bucket.nuclear_pond_targets.id

  rule {
    id     = "cleanup_old_scan_targets"
    status = "Enabled"

    # Delete scan targets after 30 days (matches DynamoDB TTL)
    expiration {
      days = 30
    }

    # Clean up incomplete multipart uploads after 7 days
    abort_incomplete_multipart_upload {
      days_after_initiation = 7
    }

    # Apply to all objects in the bucket
    filter {}
  }
}

# ==============================================================================
# BUCKET POLICY
# ==============================================================================

# Bucket policy to restrict access to Nuclear Pond ECS tasks only
resource "aws_s3_bucket_policy" "nuclear_pond_targets_policy" {
  bucket = aws_s3_bucket.nuclear_pond_targets.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowNuclearPondTaskAccess"
        Effect = "Allow"
        Principal = {
          AWS = aws_iam_role.nuclear_pond_task_role.arn
        }
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject"
        ]
        Resource = "${aws_s3_bucket.nuclear_pond_targets.arn}/*"
      },
      {
        Sid    = "AllowNuclearPondTaskListBucket"
        Effect = "Allow"
        Principal = {
          AWS = aws_iam_role.nuclear_pond_task_role.arn
        }
        Action = [
          "s3:ListBucket"
        ]
        Resource = aws_s3_bucket.nuclear_pond_targets.arn
        Condition = {
          StringLike = {
            "s3:prefix" = "scan-targets/*"
          }
        }
      },
      {
        Sid       = "DenyInsecureConnections"
        Effect    = "Deny"
        Principal = "*"
        Action    = "s3:*"
        Resource = [
          aws_s3_bucket.nuclear_pond_targets.arn,
          "${aws_s3_bucket.nuclear_pond_targets.arn}/*"
        ]
        Condition = {
          Bool = {
            "aws:SecureTransport" = "false"
          }
        }
      }
    ]
  })
}
