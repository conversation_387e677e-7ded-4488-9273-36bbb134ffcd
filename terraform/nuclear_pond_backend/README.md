# Nuclear Pond Deployment Guide

This comprehensive guide covers all deployment scenarios for Nuclear Pond infrastructure on AWS, from basic setup to production-ready deployments with monitoring and scaling.

## Overview

Nuclear Pond provides modular Terraform infrastructure that can be deployed in multiple configurations:

- **Core Lambda Function**: Nuclei execution engine with S3 storage and DynamoDB state management
- **Network Infrastructure**: VPC, subnets, and networking components
- **Nuclear Pond Backend**: Containerized API service on ECS Fargate with Application Load Balancer and S3 targets storage
- **Frontend**: Web interface with S3 and CloudFront (optional)
- **PoW Targets**: Demo infrastructure for testing (optional)


## Configuration

### Environment Variables
Set up your deployment environment:

```bash
# Required AWS configuration
export AWS_REGION=us-east-1
export AWS_PROFILE=your-aws-profile  # Optional

```

### S3 Bucket for Scan Targets

The Nuclear Pond backend automatically creates an S3 bucket for storing scan targets. This enables:

- **Scalable Target Storage**: Handle large target lists (10k+ targets) efficiently
- **Cost Optimization**: S3 storage is more cost-effective than DynamoDB for large data
- **Performance**: Faster API responses by storing only target counts in DynamoDB
- **Automatic Cleanup**: Lifecycle policies remove old scan targets after 30 days

**Key Features:**
- Bucket name: `{project_name}-{environment}-nuclear-pond-targets-{random_suffix}`
- Server-side encryption (AES256) enabled by default
- Public access blocked for security
- IAM policies restrict access to Nuclear Pond ECS tasks only
- Automatic lifecycle management (30-day retention)

## Deployment

Full deployment including network, backend service, and optional components.

```bash
# Initialize Terraform
terraform init

# Deploy core Lambda function (optional, but needed for scans functionality)
terraform apply -target=module.nuclei_lambda

# Deploy network infrastructure FIRST (required for backend)
terraform apply -target=module.network

# Deploy PoW targets (optional)
terraform apply -target=module.pow_targets -var="enable_pow_targets=true"

# Plan the deployment
terraform plan -target=module.nuclear_pond_backend -var="nuclear_pond_environment=dev" -var="enable_nuclear_pond_backend=true" -var="enable_pow_targets=true" -out=nuclear_pond_backend.out

# Deploy Nuclear Pond backend infrastructure
terraform apply nuclear_pond_backend.out

```

### Step 2: Application Deployment

Once infrastructure is deployed, deploy the Nuclear Pond application container.
The Nuclear Pond backend is deployed as a containerized service on ECS Fargate.

**Using the Generated Script (Recommended):**

```bash
# Navigate to the backend module directory
cd nuclear_pond_backend

# Deploy with the generated script
./deploy-backend-dev.sh latest

# For production with specific version
./deploy-backend-prod.sh v1.2.3
```

**Manual Deployment(need verification):**

```bash
# Get ECR repository URL
ECR_URL=$(terraform output -raw nuclear_pond_backend_ecr_repository_url)

# Build and tag the image
cd ../../nuclear_pond
docker build -t nuclear-pond:latest .
docker tag nuclear-pond:latest $ECR_URL:latest

# Login to ECR
aws ecr get-login-password --region $AWS_REGION | \
    docker login --username AWS --password-stdin $ECR_URL

# Push the image
docker push $ECR_URL:latest

# Force ECS service update
aws ecs update-service \
    --cluster $(terraform output -raw nuclear_pond_backend_ecs_cluster_name) \
    --service $(terraform output -raw nuclear_pond_backend_ecs_service_name) \
    --force-new-deployment
```

#### 2.2 Verify Application Deployment

```bash
# Get service URL
SERVICE_URL=$(terraform output -raw nuclear_pond_backend_service_url)

# Test health endpoint
curl $SERVICE_URL/health-check

# Test API with authentication
curl -H "Authorization: Bearer $TF_VAR_nuclearpond_api_key" \
     $SERVICE_URL/

```

## Environment Management

### Development Environment

```bash
# Development-specific terraform.tfvars
cat > terraform.tfvars << EOF

# Minimal resources for development
nuclear_pond_environment = "dev"
nuclear_pond_task_cpu = "256"
nuclear_pond_task_memory = "512"
nuclear_pond_desired_count = 1

# Enable demo targets for testing
enable_pow_targets = true
pow_domain_name = "fast-scan-demo-target.click"

tags = {
  Environment = "development"
  Project     = "nuclear-pond"
}
EOF
```

### Staging Environment

```bash
# Staging-specific configuration
cat > terraform.tfvars << EOF
project_name = "nuclear-pond-staging"
nuclearpond_api_key = "staging-api-key-here"

nuclear_pond_environment = "staging"
nuclear_pond_task_cpu = "512"
nuclear_pond_task_memory = "1024"
nuclear_pond_desired_count = 2

# Enable demo targets for testing
enable_pow_targets = true
pow_domain_name = "fast-scan-demo-target.click"

tags = {
  Environment = "staging"
  Project     = "nuclear-pond"
}
EOF
```

### Production Environment

```bash
# Production-specific configuration
cat > terraform.tfvars << EOF
project_name = "nuclear-pond-prod"
nuclearpond_api_key = "production-api-key-here"

nuclear_pond_environment = "prod"
nuclear_pond_task_cpu = "1024"
nuclear_pond_task_memory = "2048"
nuclear_pond_desired_count = 3

# Enhanced monitoring and security
nuclear_pond_enable_deletion_protection = true
nuclear_pond_log_retention_days = 90

# Disable PoW targets for production
enable_pow_targets = false

tags = {
  Environment = "production"
  Project     = "nuclear-pond"
  CostCenter  = "security"
}
EOF
```

## Monitoring and Operations

Effective monitoring is crucial for maintaining the health and performance of your Nuclear Pond deployment on AWS. Key services to monitor are AWS Lambda, Amazon ECS (for the backend service), Application Load Balancer, and Amazon DynamoDB.

### CloudWatch Dashboards & Key Metrics

It is highly recommended to set up a dedicated CloudWatch Dashboard for your Nuclear Pond environment.

**Key Metrics to Monitor:**

*   **AWS Lambda (`function_name`):**
    *   `Invocations`: Number of times the function is invoked.
    *   `Errors`: Number of failed invocations.
    *   `Duration`: Execution time (p50, p90, p99, max). Monitor for timeouts.
    *   `ConcurrentExecutions`: Number of concurrent instances. Monitor against account limits.
    *   `Throttles`: Number of throttled invocations.
*   **Amazon ECS Service (`nuclear_pond_backend_ecs_service_name` on `nuclear_pond_backend_ecs_cluster_name`):**
    *   `CPUUtilization`: CPU usage of the service.
    *   `MemoryUtilization`: Memory usage of the service.
    *   `RunningTaskCount`: Number of running tasks.
    *   (If using ALB) `TargetResponseTime`: Latency from the ALB.
    *   (If using ALB) `HTTPCode_Target_5XX_Count`: Server-side errors from the ECS tasks.
*   **Application Load Balancer (`nuclear_pond_backend_alb_name`):**
    *   `RequestCount`: Number of requests.
    *   `TargetConnectionErrorCount`: Errors connecting to targets (ECS tasks).
    *   `HTTPCode_ELB_5XX_Count`: Errors generated by the ALB itself.
    *   `HealthyHostCount` / `UnHealthyHostCount`: Health of registered ECS tasks.
*   **Amazon DynamoDB (`dynamodb_state_table`):**
    *   `ConsumedReadCapacityUnits` / `ConsumedWriteCapacityUnits`: Monitor against provisioned throughput.
    *   `ThrottledRequests` (or `ReadThrottleEvents`, `WriteThrottleEvents`): Indicates insufficient provisioned capacity.
    *   `SystemErrors`: Errors from the DynamoDB service.

**AWS CLI Examples for Spot-Checking Metrics (less ideal than dashboards):**
```bash
# Example: Lambda Average Duration over last hour
aws cloudwatch get-metric-statistics \
    --namespace AWS/Lambda \
    --metric-name Duration \
    --dimensions Name=FunctionName,Value=$(terraform output -raw function_name) \
    --start-time $(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%S) \
    --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
    --period 300 \
    --statistics Average

# Example: ECS Service Average CPU Utilization over last hour
aws cloudwatch get-metric-statistics \
    --namespace AWS/ECS \
    --metric-name CPUUtilization \
    --dimensions Name=ServiceName,Value=$(terraform output -raw nuclear_pond_backend_ecs_service_name),Name=ClusterName,Value=$(terraform output -raw nuclear_pond_backend_ecs_cluster_name) \
    --start-time $(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%S) \
    --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
    --period 300 \
    --statistics Average
```

Refer to the AWS CloudWatch documentation for creating dashboards and setting up alarms based on these metrics.

### Log Monitoring

Centralized logging is available through CloudWatch Logs.

```bash
# Tail logs for the core Nuclei Lambda function
aws logs tail /aws/lambda/$(terraform output -raw function_name) --follow

# Tail logs for the ECS backend service (API)
# Ensure 'nuclear_pond_backend_log_group_name' is an output from your backend module
aws logs tail $(terraform output -raw nuclear_pond_backend_log_group_name) --follow

# Filter logs for errors in the backend service
aws logs filter-log-events \
    --log-group-name $(terraform output -raw nuclear_pond_backend_log_group_name) \
    --filter-pattern "ERROR"
```

### Scaling Operations (ECS Backend Service)

The Nuclear Pond backend service running on ECS Fargate can be scaled manually or configured for auto-scaling.

#### Manual Scaling
This adjusts the desired number of tasks for the ECS service.
```bash
# Scale ECS service to 5 tasks
aws ecs update-service \
    --cluster $(terraform output -raw nuclear_pond_backend_ecs_cluster_name) \
    --service $(terraform output -raw nuclear_pond_backend_ecs_service_name) \
    --desired-count 5

# Check scaling status
aws ecs describe-services \
    --cluster $(terraform output -raw nuclear_pond_backend_ecs_cluster_name) \
    --services $(terraform output -raw nuclear_pond_backend_ecs_service_name) \
    --query 'services[0].{RunningCount: runningCount, DesiredCount: desiredCount, Status: status, Deployments: deployments}'
```

#### Auto Scaling Configuration (via Terraform)

Define Application Auto Scaling in your Terraform configuration for the ECS service. This typically involves:
1.  `aws_appautoscaling_target`: Defines the scalable resource (ECS service desired count) and min/max capacity.
2.  `aws_appautoscaling_policy`: Defines the scaling policy (e.g., Target Tracking based on CPU or Memory utilization, or Step Scaling).

Example Target Tracking Policy (conceptual, adapt to your module structure):
```hcl
# (This would be part of your nuclear_pond_backend module or main configuration)

# resource "aws_ecs_cluster" "cluster" { ... }
# resource "aws_ecs_service" "service" { ... }

resource "aws_appautoscaling_target" "ecs_service_target" {
  max_capacity       = var.ecs_autoscale_max_instances    # e.g., 10
  min_capacity       = var.ecs_autoscale_min_instances    # e.g., 2
  resource_id        = "service/${aws_ecs_cluster.cluster.name}/${aws_ecs_service.service.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"
}

resource "aws_appautoscaling_policy" "ecs_cpu_scaling_policy" {
  name               = "${var.project_name}-scale-on-cpu"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.ecs_service_target.resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_service_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.ecs_service_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    target_value       = var.ecs_autoscale_cpu_target_percentage # e.g., 70.0 (scale up if CPU > 70%)
    scale_in_cooldown  = 300 # seconds
    scale_out_cooldown = 60  # seconds
  }
}

# Optionally, add a similar policy for Memory Utilization
```
Ensure you have variables like `ecs_autoscale_max_instances`, `ecs_autoscale_min_instances`, and `ecs_autoscale_cpu_target_percentage` defined.

## Troubleshooting

This section addresses common issues encountered during the deployment or operation of the Nuclear Pond AWS infrastructure.

### Common Infrastructure Deployment Issues

#### 1. S3 Bucket Name Conflicts

-   **Error Indication**: `BucketAlreadyExists: The requested bucket name is not available` during `terraform apply`.
-   **Cause**: S3 bucket names must be globally unique. The `project_name` variable often contributes to the bucket name.
-   **Solution**: Modify the `project_name` in your `terraform.tfvars` or as an environment variable to ensure uniqueness.
    ```bash
    # Example: In terraform.tfvars
    # project_name = "nuclear-pond-unique-suffix"

    # Or as an environment variable for TF apply
    export TF_VAR_project_name="nuclear-pond-$(date +%s)"
    terraform apply
    ```

#### 2. Lambda Function Build or Deployment Failures

-   **Error Indication**: Errors during `terraform apply` related to Lambda function resource creation, often mentioning issues with packaging, code storage, or IAM permissions.
-   **Cause**:
    - Problems with the Lambda deployment package (e.g., if using a local build script like `null_resource.build` that fails).
    - Incorrect IAM permissions for Terraform to create Lambda functions or for the Lambda function's execution role.
    - S3 source object for Lambda code (if applicable) not found or inaccessible.
-   **Troubleshooting Steps**:
    -   Examine Terraform error output for specific details.
    -   If using a local build script (e.g., for `lambda-nuclei-scanner`), try running the build steps manually to isolate issues.
        ```bash
        # Example if a build script exists for the Lambda code
        # cd ../../lambda-nuclei-scanner # Navigate to lambda source
        # ./build.sh # Or equivalent build command
        # cd ../terraform/nuclear_pond_backend # Navigate back
        ```
    -   If `null_resource.build` is used, you can try to force its re-execution:
        ```bash
        terraform apply -replace=null_resource.build # Adjust resource name if different
        ```
    -   Verify IAM permissions for your Terraform execution role/user.
    -   Check CloudFormation (if Terraform uses it behind the scenes for Lambda) or Lambda console for more detailed error messages.

#### 3. ECS Task Startup Failures

-   **Error Indication**: ECS tasks for the backend service repeatedly stopping and starting, failing health checks, or service deployment stuck.
-   **Cause**:
    - Container image issues (not found in ECR, invalid image, or app crashing on start).
    - Incorrect ECS task definition (CPU/memory, environment variables, port mappings).
    - Networking misconfiguration (VPC, subnets, security groups blocking traffic to/from the task, or to dependencies like DynamoDB).
    - IAM task role permissions insufficient for the application (e.g., to access DynamoDB, S3).
    - Application Load Balancer (ALB) health check failures.
-   **Troubleshooting Steps**:
    -   **Check ECS Service Events**: In the AWS Console (ECS -> Cluster -> Service -> Events tab) for reasons why tasks are stopping.
    -   **Check ECS Task Logs**: Retrieve logs from stopped or running tasks.
        ```bash
        # List tasks for the service
        TASK_ARNS=$(aws ecs list-tasks \
            --cluster $(terraform output -raw nuclear_pond_backend_ecs_cluster_name) \
            --service-name $(terraform output -raw nuclear_pond_backend_ecs_service_name) \
            --query 'taskArns' --output text)

        # Describe a specific task (get one ARN from above)
        # aws ecs describe-tasks --cluster $(...) --tasks <TASK_ARN>

        # View logs (ensure nuclear_pond_backend_log_group_name is correct output)
        aws logs tail $(terraform output -raw nuclear_pond_backend_log_group_name) --follow
        ```
    -   **Verify ECR Image**: Ensure the Docker image specified in the task definition exists in the ECR repository and was pushed successfully.
        ```bash
        aws ecr describe-images \
            --repository-name $(terraform output -raw nuclear_pond_backend_ecr_repository_name) \
            --image-ids imageTag=latest # Or your specific image tag
        ```
    -   **Check ALB Target Group Health Checks**: Verify health check settings on the ALB target group and ensure the application responds correctly to the health check path (e.g., `/health-check`).
    -   **Test Network Connectivity**: From within a similar environment (e.g., a temporary EC2 instance in the same VPC/subnets/SGs) to required services like DynamoDB.

#### 4. API Connectivity or Configuration Issues

-   **Error Indication**: `401 Unauthorized`, `403 Forbidden`, or connection timeouts when trying to access the deployed API service URL.
-   **Cause**:
    - Incorrect `TF_VAR_nuclearpond_api_key` set during `terraform apply`, leading to the wrong API key in the ECS task environment variables.
    - ALB Security Group not allowing traffic from your IP or expected sources on the listener port (e.g., 80/443).
    - ECS Task Security Group not allowing traffic from the ALB on the container port (e.g., 8082).
    - DNS issues if using a custom domain name for the service URL.
-   **Troubleshooting Steps**:
    -   **Verify API Key Provisioning**: Check the ECS task definition in the AWS console to see the environment variables passed to the container. *Do not log the actual key value if possible.* Ensure the key you are using as a client matches what was provisioned.
    -   **Test with `curl` from an allowed IP (e.g., EC2 instance in VPC)**:
        ```bash
        SERVICE_URL=$(terraform output -raw nuclear_pond_backend_service_url)
        # Test health check (no auth)
        curl $SERVICE_URL/health-check
        # Test authenticated endpoint (replace with your actual key)
        curl -H "Authorization: Bearer <your_supposed_api_key>" $SERVICE_URL/
        ```
    -   **Review Security Groups**: Check inbound rules for ALB and ECS service security groups.
    -   **Check ALB Listeners and Target Groups**: Ensure listeners are correctly forwarding to the target group, and the target group shows healthy targets.

### Adjusting Resource Configurations via Terraform

If you encounter performance bottlenecks or errors related to resource limits, you may need to adjust the configuration of your Lambda functions or ECS tasks via Terraform variables.

#### Lambda Timeout / Memory Configuration

-   **Issue**: Lambda functions timing out (look for `Task timed out after X.XX seconds` in logs) or running out of memory.
-   **Solution**: Increase the Lambda timeout or memory via Terraform variables in your `terraform.tfvars` or command line, then re-apply.
    ```bash
    # Example: Increase Lambda timeout to 30 minutes (900s -> 1800s)
    # In terraform.tfvars: nuclei_timeout = 1800
    # Or via CLI:
    terraform apply -var="nuclei_timeout=1800"

    # Example: Increase Lambda memory (ensure 'memory_size' is a variable in your module)
    # In terraform.tfvars: memory_size = 1024
    # Or via CLI:
    terraform apply -var="memory_size=1024"
    ```
-   **Monitoring**: Check Lambda `Duration` and `MaxMemoryUsed` metrics in CloudWatch.
    ```bash
    aws cloudwatch get-metric-statistics \
        --namespace AWS/Lambda \
        --metric-name Duration \
        --dimensions Name=FunctionName,Value=$(terraform output -raw function_name) \
        --start-time $(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%S) \
        --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
        --period 300 \
        --statistics Maximum Average
    ```

#### ECS Task CPU / Memory Configuration

-   **Issue**: ECS tasks for the backend service are hitting CPU or memory limits, leading to poor performance or tasks being stopped.
-   **Solution**: Increase ECS task CPU or memory definitions in your Terraform configuration.
    ```bash
    # Example: In terraform.tfvars (assuming these vars exist in your backend module)
    # nuclear_pond_task_cpu = "1024"    # 1 vCPU
    # nuclear_pond_task_memory = "2048" # 2GB Memory

    # Or via CLI:
    terraform apply -var="nuclear_pond_task_cpu=1024" -var="nuclear_pond_task_memory=2048"
    ```
-   **Monitoring**: Check ECS `CPUUtilization` and `MemoryUtilization` metrics in CloudWatch for your service.
    ```bash
    aws cloudwatch get-metric-statistics \
        --namespace AWS/ECS \
        --metric-name MemoryUtilization \
        --dimensions Name=ServiceName,Value=$(terraform output -raw nuclear_pond_backend_ecs_service_name),Name=ClusterName,Value=$(terraform output -raw nuclear_pond_backend_ecs_cluster_name) \
        --start-time $(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%S) \
        --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
        --period 300 \
        --statistics Maximum Average
    ```
