#!/bin/bash
# ==============================================================================
# ECR CLEANUP SCRIPT
# ==============================================================================
# This script helps clean up ECR repositories before running terraform destroy
# Use this if you encounter issues with ECR repositories not being empty

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PROJECT_NAME=""
ENVIRONMENT=""
REGION=""
DRY_RUN=false

# Function to display usage
usage() {
    echo "Usage: $0 -p PROJECT_NAME -e ENVIRONMENT -r REGION [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -p, --project-name    Project name (required)"
    echo "  -e, --environment     Environment (required)"
    echo "  -r, --region          AWS region (required)"
    echo "  -d, --dry-run         Show what would be deleted without actually deleting"
    echo "  -h, --help            Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 -p nuclear-pond-test -e dev -r us-east-1"
    echo "  $0 -p nuclear-pond-test -e dev -r us-east-1 --dry-run"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--project-name)
            PROJECT_NAME="$2"
            shift 2
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$PROJECT_NAME" || -z "$ENVIRONMENT" || -z "$REGION" ]]; then
    echo -e "${RED}Error: Missing required parameters${NC}"
    usage
    exit 1
fi

# Construct repository name
REPOSITORY_NAME="${PROJECT_NAME}/nuclearpond"

echo -e "${BLUE}=== ECR Cleanup Script ===${NC}"
echo -e "${BLUE}Project: ${PROJECT_NAME}${NC}"
echo -e "${BLUE}Environment: ${ENVIRONMENT}${NC}"
echo -e "${BLUE}Region: ${REGION}${NC}"
echo -e "${BLUE}Repository: ${REPOSITORY_NAME}${NC}"
echo -e "${BLUE}Dry Run: ${DRY_RUN}${NC}"
echo ""

# Check if AWS CLI is available
if ! command -v aws &> /dev/null; then
    echo -e "${RED}Error: AWS CLI is not installed or not in PATH${NC}"
    exit 1
fi

# Check if repository exists
echo -e "${YELLOW}Checking if ECR repository exists...${NC}"
if ! aws ecr describe-repositories --repository-names "$REPOSITORY_NAME" --region "$REGION" &> /dev/null; then
    echo -e "${GREEN}Repository ${REPOSITORY_NAME} does not exist or is already deleted${NC}"
    exit 0
fi

# List images in the repository
echo -e "${YELLOW}Listing images in repository...${NC}"
IMAGES=$(aws ecr list-images --repository-name "$REPOSITORY_NAME" --region "$REGION" --output json)
IMAGE_COUNT=$(echo "$IMAGES" | jq '.imageIds | length')

if [[ "$IMAGE_COUNT" -eq 0 ]]; then
    echo -e "${GREEN}Repository ${REPOSITORY_NAME} is already empty${NC}"
    exit 0
fi

echo -e "${YELLOW}Found ${IMAGE_COUNT} images in repository${NC}"

# Show images that will be deleted
echo -e "${YELLOW}Images to be deleted:${NC}"
echo "$IMAGES" | jq -r '.imageIds[] | "  - Tag: \(.imageTag // "untagged"), Digest: \(.imageDigest[0:12])..."'

if [[ "$DRY_RUN" == true ]]; then
    echo ""
    echo -e "${BLUE}=== DRY RUN MODE ===${NC}"
    echo -e "${BLUE}The above images would be deleted. Run without --dry-run to actually delete them.${NC}"
    exit 0
fi

# Confirm deletion
echo ""
read -p "Are you sure you want to delete all images from ${REPOSITORY_NAME}? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Operation cancelled${NC}"
    exit 0
fi

# Delete all images
echo -e "${YELLOW}Deleting all images from repository...${NC}"
if aws ecr batch-delete-image \
    --repository-name "$REPOSITORY_NAME" \
    --region "$REGION" \
    --image-ids "$(echo "$IMAGES" | jq -c '.imageIds')" > /dev/null; then
    echo -e "${GREEN}Successfully deleted all images from ${REPOSITORY_NAME}${NC}"
else
    echo -e "${RED}Failed to delete images from ${REPOSITORY_NAME}${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}=== Cleanup Complete ===${NC}"
echo -e "${GREEN}You can now run 'terraform destroy' successfully${NC}" 