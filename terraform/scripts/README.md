# Terraform Destroy Fix for ECR Repository

## Problem

When running `terraform destroy`, you may encounter the following error:

```
Error: ECR Repository (nuclear-pond-test/nuclearpond) not empty, consider using force_delete: RepositoryNotEmptyException: The repository with name 'nuclear-pond-test/nuclearpond' in registry with id '067339277019' cannot be deleted because it still contains images
```

This happens because AWS ECR repositories cannot be deleted if they contain Docker images, and by default, Terraform doesn't force delete repositories with images.

## Solution

### Option 1: Automatic Fix (Recommended)

The ECR repository configuration has been updated to include `force_delete = true`, which allows Terraform to automatically delete the repository even if it contains images.

**Changes made:**
- Added `force_delete = var.ecr_force_delete` to the ECR repository resource
- Added `ecr_force_delete` variable with default value `true`

After applying these changes, run:

```bash
# Apply the configuration changes first
terraform apply

# Then destroy will work without issues
terraform destroy
```

### Option 2: Manual Cleanup

If you prefer to manually clean up the ECR repository before destroying, use the provided cleanup script:

```bash
# Make the script executable
chmod +x terraform/scripts/cleanup-ecr.sh

# Run the cleanup script (dry run first to see what will be deleted)
./terraform/scripts/cleanup-ecr.sh -p nuclear-pond-test -e dev -r us-east-1 --dry-run

# If the dry run looks good, run the actual cleanup
./terraform/scripts/cleanup-ecr.sh -p nuclear-pond-test -e dev -r us-east-1

# Then run terraform destroy
terraform destroy
```

### Option 3: AWS CLI Manual Cleanup

You can also manually delete images using AWS CLI:

```bash
# List images in the repository
aws ecr list-images --repository-name nuclear-pond-test/nuclearpond --region us-east-1

# Delete all images
aws ecr batch-delete-image \
    --repository-name nuclear-pond-test/nuclearpond \
    --region us-east-1 \
    --image-ids "$(aws ecr list-images --repository-name nuclear-pond-test/nuclearpond --region us-east-1 --query 'imageIds[*]' --output json)"

# Then run terraform destroy
terraform destroy
```

## Script Usage

The `cleanup-ecr.sh` script supports the following options:

- `-p, --project-name`: Project name (required)
- `-e, --environment`: Environment (required) 
- `-r, --region`: AWS region (required)
- `-d, --dry-run`: Show what would be deleted without actually deleting
- `-h, --help`: Show help message

## Prevention

To prevent this issue in the future, ensure that:

1. The `ecr_force_delete` variable is set to `true` (default)
2. Consider implementing proper CI/CD cleanup processes
3. Use ECR lifecycle policies to automatically clean up old images

## Files Modified

- `terraform/nuclear_pond_backend/ecr.tf`: Added `force_delete` parameter
- `terraform/nuclear_pond_backend/variables.tf`: Added `ecr_force_delete` variable
- `terraform/scripts/cleanup-ecr.sh`: New cleanup script
- `terraform/scripts/README.md`: This documentation 