# Headers to include with all HTTP request
header:
  - 'X-BugBounty-Hacker: fastscan'

# Directory based template execution
templates:
  - /opt # Tell Nuclei to look in the /opt directory for template files.
         # Custom templates layer unpacks .yaml files directly into /opt.

# Lambda-specific configurations
# Set home directory to /tmp (only writable directory in Lambda)
home: /tmp

# Disable cloud upload warnings
cloud-upload: false
disable-update-check: true

# Add explicit template configuration
templates-directory: /opt/nuclei-templates
update-templates: false

# Output configuration
json-export: true
no-color: true

# Performance settings
rate-limit: 500
bulk-size: 50
concurrency: 50
timeout: 10
retries: 0