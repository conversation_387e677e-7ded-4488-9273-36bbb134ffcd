# ==============================================================================
# NUCLEI LAMBDA MODULE VARIABLES
# ==============================================================================

# ==============================================================================
# CORE PROJECT VARIABLES
# ==============================================================================

variable "project_name" {
  description = "Name of the project, used for resource naming (e.g., 'fast-scan', 'nuclei-scanner')"
  type        = string

  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.project_name))
    error_message = "Project name must contain only lowercase letters, numbers, and hyphens."
  }
}

variable "environment" {
  description = "Deployment environment - affects resource naming and configuration"
  type        = string
  default     = "dev"

  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, prod."
  }
}

variable "tags" {
  description = "Common tags to apply to all resources for cost tracking and organization"
  type        = map(string)
  default     = {}

  # Example:
  # tags = {
  #   Project     = "nuclei-scanner"
  #   Environment = "dev"
  #   Owner       = "security-team"
  #   CostCenter  = "security"
  # }
}

# ==============================================================================
# NUCLEI CONFIGURATION
# ==============================================================================

variable "nuclei_version" {
  description = <<-EOT
    Version of Nuclei scanner to download and use.

    This should match a valid release version from the Nuclei GitHub repository.
    The module will download the binary from the official releases.

    Examples: "3.1.7", "3.2.0", "3.1.6"
  EOT
  type        = string
  default     = "3.1.7"

  validation {
    condition     = can(regex("^[0-9]+\\.[0-9]+\\.[0-9]+$", var.nuclei_version))
    error_message = "Nuclei version must be in semantic version format (e.g., 3.1.7)."
  }
}

variable "nuclei_arch" {
  description = <<-EOT
    Architecture of the Nuclei binary to download.

    Must match the Lambda runtime architecture (linux_amd64 for x86_64).
    Available options depend on Nuclei release assets.
  EOT
  type        = string
  default     = "linux_amd64"

  validation {
    condition     = contains(["linux_amd64", "linux_arm64"], var.nuclei_arch)
    error_message = "Nuclei architecture must be one of: linux_amd64, linux_arm64."
  }
}

# ==============================================================================
# LAMBDA FUNCTION CONFIGURATION
# ==============================================================================

variable "nuclei_timeout" {
  description = <<-EOT
    Lambda function timeout in seconds.

    This determines how long a single Nuclei scan can run before being terminated.
    Consider the complexity of your scans when setting this value.

    AWS Lambda maximum timeout is 900 seconds (15 minutes).
  EOT
  type        = number
  default     = 900

  validation {
    condition     = var.nuclei_timeout >= 1 && var.nuclei_timeout <= 900
    error_message = "Lambda timeout must be between 1 and 900 seconds."
  }
}

variable "memory_size" {
  description = <<-EOT
    Memory allocation for the Lambda function in MB.

    Higher memory allocation also provides more CPU power.
    Nuclei scans can be memory-intensive depending on the number of templates.

    AWS Lambda memory range: 128 MB to 10,240 MB (10 GB).
  EOT
  type        = number
  default     = 512

  validation {
    condition     = var.memory_size >= 128 && var.memory_size <= 10240
    error_message = "Lambda memory size must be between 128 and 10240 MB."
  }
}

variable "lambda_runtime" {
  description = "Lambda runtime for the Nuclei function"
  type        = string
  default     = "provided.al2"

  validation {
    condition     = contains(["provided.al2", "provided"], var.lambda_runtime)
    error_message = "Lambda runtime must be provided.al2 or provided for custom runtimes."
  }
}

# ==============================================================================
# S3 BUCKET CONFIGURATION
# ==============================================================================

variable "enable_s3_versioning" {
  description = <<-EOT
    Whether to enable versioning on the S3 artifacts bucket.

    Versioning provides protection against accidental deletion or modification
    but increases storage costs. Recommended for production environments.
  EOT
  type        = bool
  default     = false
}

variable "findings_retention_days" {
  description = <<-EOT
    Number of days to retain scan findings in S3 before automatic deletion.

    Set to 0 to disable automatic deletion.
    Consider compliance and audit requirements when setting this value.
  EOT
  type        = number
  default     = 90

  validation {
    condition     = var.findings_retention_days >= 0
    error_message = "Findings retention days must be 0 or greater."
  }
}

variable "enable_s3_notifications" {
  description = "Whether to enable S3 bucket notifications for EventBridge"
  type        = bool
  default     = false
}

variable "enable_bucket_policy" {
  description = "Whether to enable additional S3 bucket policy for security"
  type        = bool
  default     = false
}

# ==============================================================================
# CLOUDWATCH CONFIGURATION
# ==============================================================================

variable "log_retention_days" {
  description = <<-EOT
    Number of days to retain CloudWatch logs for the Lambda function.

    Longer retention periods increase costs but provide better debugging capabilities.
    Common values: 1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653
  EOT
  type        = number
  default     = 30

  validation {
    condition = contains([
      1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653
    ], var.log_retention_days)
    error_message = "Log retention days must be one of the valid CloudWatch retention periods."
  }
}

variable "enable_cloudwatch_alarms" {
  description = "Whether to create CloudWatch alarms for Lambda monitoring"
  type        = bool
  default     = false
}

variable "enable_cloudwatch_dashboard" {
  description = "Whether to create a CloudWatch dashboard for monitoring"
  type        = bool
  default     = false
}

variable "enable_log_insights_queries" {
  description = "Whether to create saved CloudWatch Insights queries"
  type        = bool
  default     = false
}

variable "cloudwatch_alarm_actions" {
  description = "List of ARNs to notify when CloudWatch alarms trigger"
  type        = list(string)
  default     = []
}

# ==============================================================================
# TEMPLATES CONFIGURATION
# ==============================================================================

variable "custom_templates_path" {
  description = <<-EOT
    Path to custom Nuclei templates directory relative to the module.

    This directory will be zipped and uploaded as a Lambda layer.
    Default points to the nuclear_pond templates directory.
  EOT
  type        = string
  default     = "../../nuclear_pond/templates/"
}

variable "nuclei_config_path" {
  description = "Path to Nuclei configuration files directory"
  type        = string
  default     = "../config"
}

# ==============================================================================
# PERFORMANCE AND SCALING
# ==============================================================================

variable "enable_lambda_insights" {
  description = <<-EOT
    Whether to enable AWS Lambda Insights for enhanced monitoring.

    Lambda Insights provides detailed performance metrics and logs
    but incurs additional costs. Recommended for production environments.
  EOT
  type        = bool
  default     = false
}

variable "reserved_concurrent_executions" {
  description = <<-EOT
    Number of reserved concurrent executions for the Lambda function.

    Set to -1 for unreserved concurrency (default).
    Set to 0 to disable the function.
    Set to a positive number to reserve specific concurrency.
  EOT
  type        = number
  default     = -1

  validation {
    condition     = var.reserved_concurrent_executions >= -1
    error_message = "Reserved concurrent executions must be -1 or greater."
  }
}

variable "enable_vpc_execution" {
  description = <<-EOT
    Whether to enable VPC execution for the Lambda function.

    Set to true if the Lambda function needs to access resources in a VPC.
    This will attach the necessary VPC execution role policy.
  EOT
  type        = bool
  default     = false
}
