# Nuclei Lambda Module

This Terraform module deploys a complete Nuclei scanner infrastructure on AWS Lambda with all required components including S3 storage, Lambda layers, IAM roles, and monitoring capabilities.

## Architecture

The module creates a serverless Nuclei scanning infrastructure:

- **AWS Lambda Function**: Serverless execution environment for Nuclei scans
- **Lambda Layers**: Separate layers for Nuclei binary, custom templates, and configurations
- **S3 Bucket**: Storage for artifacts, templates, and scan findings
- **IAM Roles**: Least-privilege access for Lambda execution
- **CloudWatch**: Comprehensive logging and optional monitoring/alerting
- **AWS Glue**: Data catalog for scan findings analysis

## Quick Start

### 1. Deploy

```bash
# Initialize Terraform
terraform init

# Plan deployment
terraform plan -target=module.nuclei_lambda

# Deploy the module
terraform apply -target=module.nuclei_lambda
```

## Module Overview

This module encapsulates all AWS resources required to run the Nuclei scanner as a Lambda function. It is designed for independent deployment, follows consistent project patterns, and offers robust security and monitoring features.

### Core Features
- **Lambda Function**: Serverless Nuclei scanner execution using a Go-based application.
- **Lambda Layers**: Manages separate layers for the Nuclei binary, custom user templates, and Nuclei configuration files.
- **S3 Storage**: Provides a dedicated S3 bucket for storing Nuclei artifacts (binaries, templates, configs) and scan findings. Includes lifecycle management for cost optimization.
- **IAM Security**: Implements least-privilege IAM roles and policies for secure access to AWS resources.
- **AWS Glue Integration**: Optionally creates a Glue Data Catalog for querying scan findings stored in S3, facilitating analysis.
- **Comprehensive Monitoring**: Integrates with CloudWatch for logging, and optionally provides alarms, dashboards, and Lambda Insights for enhanced observability.

## Module Structure

```
nuclei_lambda/
├── main.tf              # Main Lambda function and Glue resources
├── variables.tf         # Input variables with validation
├── outputs.tf          # Output values for other modules
├── iam.tf              # IAM roles and policies
├── s3.tf               # S3 bucket and lifecycle policies
├── artifacts.tf        # Nuclei binary and template management
├── cloudwatch.tf       # CloudWatch logs, alarms, and dashboards
└── README.md           # This documentation
```

## Input Variables

### Required Variables

| Name | Description | Type |
|------|-------------|------|
| `project_name` | Name of the project for resource naming | `string` |

### Core Configuration

| Name | Description | Type | Default |
|------|-------------|------|---------|
| `environment` | Deployment environment (dev/staging/prod) | `string` | `"dev"` |
| `nuclei_version` | Nuclei scanner version to use | `string` | `"3.1.7"` |
| `nuclei_timeout` | Lambda timeout in seconds (max 900) | `number` | `900` |
| `memory_size` | Lambda memory in MB (128-10240) | `number` | `512` |

### Storage Configuration

| Name | Description | Type | Default |
|------|-------------|------|---------|
| `enable_s3_versioning` | Enable S3 bucket versioning | `bool` | `false` |
| `findings_retention_days` | Days to retain findings (0=never delete) | `number` | `90` |

### Monitoring Configuration

| Name | Description | Type | Default |
|------|-------------|------|---------|
| `log_retention_days` | CloudWatch log retention days | `number` | `30` |
| `enable_cloudwatch_alarms` | Create CloudWatch alarms | `bool` | `false` |
| `enable_cloudwatch_dashboard` | Create monitoring dashboard | `bool` | `false` |
| `enable_lambda_insights` | Enable Lambda Insights | `bool` | `false` |

For a complete list of variables, see [variables.tf](./variables.tf).

## Outputs

### Key Outputs

| Name | Description |
|------|-------------|
| `lambda_function_name` | Name of the Lambda function |
| `lambda_function_arn` | ARN of the Lambda function |
| `s3_bucket_name` | Name of the artifacts S3 bucket |
| `findings_s3_path` | S3 path for scan findings |
| `cloudwatch_log_group_name` | CloudWatch log group name |


## Monitoring and Observability

### CloudWatch Logs

All Lambda execution logs are stored in CloudWatch with configurable retention:

```bash
# View logs
aws logs tail /aws/lambda/fast-scan-nuclei-function --follow
```

### Useful Commands

```bash
# Check Lambda function status
aws lambda get-function --function-name fast-scan-nuclei-function

# Check DynamoDB table
aws dynamodb describe-table --table-name $(terraform output -raw dynamodb_state_table)

# Check S3 bucket
aws s3 ls $(terraform output -raw s3_bucket_name)

# View recent logs
aws logs tail /aws/lambda/fast-scan-nuclei-function --follow

# Test Lambda function
aws lambda invoke --function-name fast-scan-nuclei-function \
  --payload '{"target": "example.com"}' response.json

# Check S3 bucket contents
aws s3 ls s3://fast-scan-nuclei-artifacts/ --recursive

# Monitor CloudWatch metrics
aws cloudwatch get-metric-statistics \
  --namespace AWS/Lambda \
  --metric-name Duration \
  --dimensions Name=FunctionName,Value=fast-scan-nuclei-function \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-02T00:00:00Z \
  --period 3600 \
  --statistics Average
```

## Contributing

When modifying this module:

1. Update variable descriptions and validation rules
2. Update documentation for any new features
3. Follow best practices

## Related Documentation

- [Nuclei Lambda Application README](../../lambda-nuclei-scanner/README.md)
- [Nuclear Pond Backend Module](../nuclear_pond_backend/README.md)
- [Network Module](../network/README.md)
- [Nuclei Documentation](https://docs.projectdiscovery.io/tools/nuclei/)
