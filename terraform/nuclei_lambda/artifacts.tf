# ==============================================================================
# NUCLEI ARTIFACTS MANAGEMENT
# ==============================================================================
# This file manages the download, packaging, and upload of Nuclei-related
# artifacts including the binary, templates, and configuration files.

# ==============================================================================
# NUCLEI BINARY DOWNLOAD AND UPLOAD
# ==============================================================================

# Download Nuclei binary from GitHub releases
resource "null_resource" "download_nuclei" {
  triggers = {
    version      = var.nuclei_version
    architecture = var.nuclei_arch
    # Force re-download if file doesn't exist or is corrupted
    always_run   = timestamp()
  }

  provisioner "local-exec" {
    command = <<-EOT
      echo "Downloading Nuclei v${var.nuclei_version} for ${var.nuclei_arch}..."

      # Clean up any existing files first
      rm -f "${path.module}/nuclei.zip"
      rm -f "${path.module}/nuclei_extracted"

      # Download with better error handling
      if ! curl -L -f -o "${path.module}/nuclei.zip" \
        "https://github.com/projectdiscovery/nuclei/releases/download/v${var.nuclei_version}/nuclei_${var.nuclei_version}_${var.nuclei_arch}.zip"; then
        echo "Error: Failed to download Nuclei binary from GitHub"
        echo "URL: https://github.com/projectdiscovery/nuclei/releases/download/v${var.nuclei_version}/nuclei_${var.nuclei_version}_${var.nuclei_arch}.zip"
        exit 1
      fi

      # Verify download was successful and file is not empty
      if [ ! -f "${path.module}/nuclei.zip" ] || [ ! -s "${path.module}/nuclei.zip" ]; then
        echo "Error: Downloaded file is missing or empty"
        exit 1
      fi

      # Test that the zip file is valid and extract to verify binary
      mkdir -p "${path.module}/nuclei_extracted"
      if ! unzip -q "${path.module}/nuclei.zip" -d "${path.module}/nuclei_extracted"; then
        echo "Error: Downloaded zip file is corrupted"
        rm -f "${path.module}/nuclei.zip"
        exit 1
      fi

      # Verify the nuclei binary exists and is executable
      if [ ! -f "${path.module}/nuclei_extracted/nuclei" ]; then
        echo "Error: Nuclei binary not found in downloaded archive"
        ls -la "${path.module}/nuclei_extracted/"
        exit 1
      fi

      # Check binary version to ensure it's correct
      chmod +x "${path.module}/nuclei_extracted/nuclei"
      ACTUAL_VERSION=$("${path.module}/nuclei_extracted/nuclei" -version 2>&1 | grep -o 'v[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1)
      EXPECTED_VERSION="v${var.nuclei_version}"

      if [ "$ACTUAL_VERSION" != "$EXPECTED_VERSION" ]; then
        echo "Error: Version mismatch. Expected $EXPECTED_VERSION, got $ACTUAL_VERSION"
        exit 1
      fi

      # Clean up extraction directory
      rm -rf "${path.module}/nuclei_extracted"

      echo "Successfully downloaded and verified Nuclei v${var.nuclei_version}"
      echo "File size: $(stat -c%s "${path.module}/nuclei.zip") bytes"
    EOT
  }

  # Clean up on destroy
  provisioner "local-exec" {
    when    = destroy
    command = "rm -f ${path.module}/nuclei.zip ${path.module}/nuclei_extracted"
  }
}

# Upload Nuclei binary to S3
resource "aws_s3_object" "upload_nuclei" {
  depends_on = [null_resource.download_nuclei]

  bucket = aws_s3_bucket.artifacts_bucket.id
  key    = "nuclei.zip"
  source = "${path.module}/nuclei.zip"

  # Use source_hash instead of etag to avoid file dependency issues during planning
  source_hash = "${var.nuclei_version}-${var.nuclei_arch}"

  tags = merge(var.tags, {
    Name        = "nuclei-binary"
    Component   = "nuclei-lambda"
    Environment = var.environment
    Type        = "artifact"
    Version     = var.nuclei_version
  })
}

# ==============================================================================
# CUSTOM NUCLEI TEMPLATES
# ==============================================================================

# Force rebuild of custom templates every apply
resource "null_resource" "force_templates_rebuild" {
  triggers = {
    # Force rebuild on every apply
    always_run = timestamp()
  }
}

# Package custom Nuclei templates
data "archive_file" "custom_nuclei_templates_zip" {
  depends_on = [null_resource.force_templates_rebuild]
  
  type        = "zip"
  source_dir  = var.custom_templates_path
  output_path = "${path.module}/custom-nuclei-templates.zip"

  # Keep it simple - exclude common non-template files but allow both .yaml and .yml
  excludes = [
    "*.md",
    "*.txt",
    "*.log",
    "*.json",
    ".git*",
    ".DS_Store",
    "Thumbs.db",
    "*.tmp"
  ]
}

# Upload custom templates to S3
resource "aws_s3_object" "upload_templates" {
  depends_on = [data.archive_file.custom_nuclei_templates_zip]

  bucket = aws_s3_bucket.artifacts_bucket.id
  key    = "custom-nuclei-templates.zip"
  source = data.archive_file.custom_nuclei_templates_zip.output_path

  # Use etag from archive_file for change detection (this works better with S3)
  etag = data.archive_file.custom_nuclei_templates_zip.output_md5

  tags = merge(var.tags, {
    Name        = "custom-nuclei-templates"
    Component   = "nuclei-lambda"
    Environment = var.environment
    Type        = "artifact"
    Purpose     = "custom-templates"
  })
}

# ==============================================================================
# NUCLEI CONFIGURATION FILES
# ==============================================================================

# Package Nuclei configuration files
data "archive_file" "nuclei_config" {
  type        = "zip"
  source_dir  = var.nuclei_config_path
  output_path = "${path.module}/nuclei-configs.zip"

  # Exclude non-config files
  excludes = [
    "*.md",
    "*.txt",
    "*.log",
    ".git*",
    ".DS_Store",
    "Thumbs.db"
  ]
}

# Upload configuration files to S3
resource "aws_s3_object" "upload_config" {
  depends_on = [data.archive_file.nuclei_config]

  bucket = aws_s3_bucket.artifacts_bucket.id
  key    = "nuclei-configs.zip"
  source = data.archive_file.nuclei_config.output_path

  # Generate ETag based on file content for change detection
  etag = data.archive_file.nuclei_config.output_md5

  tags = merge(var.tags, {
    Name        = "nuclei-configs"
    Component   = "nuclei-lambda"
    Environment = var.environment
    Type        = "artifact"
    Purpose     = "configuration"
  })
}

# ==============================================================================
# LAMBDA FUNCTION CODE
# ==============================================================================

# Build the Lambda function binary
resource "null_resource" "build_lambda" {
  triggers = {
    # Rebuild when source code changes
    always = timestamp()
  }

  provisioner "local-exec" {
    command = <<-EOT
      echo "Building Lambda function..."
      cd "${path.module}/../../lambda-nuclei-scanner"

      # Ensure Go modules are available
      if [ ! -f "go.mod" ]; then
        echo "Error: go.mod not found in lambda-nuclei-scanner directory"
        exit 1
      fi

      # Build for Lambda runtime from new cmd/lambda directory
      cd cmd/lambda
      GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o ../../bootstrap

      # Verify build was successful
      if [ ! -f "../../bootstrap" ]; then
        echo "Error: Failed to build Lambda function"
        exit 1
      fi

      echo "Successfully built Lambda function from cmd/lambda"
    EOT
  }

  # Clean up on destroy
  provisioner "local-exec" {
    when    = destroy
    command = "rm -f ${path.module}/../../lambda-nuclei-scanner/bootstrap"
  }
}

# Package Lambda function code
data "archive_file" "lambda_zip" {
  depends_on  = [null_resource.build_lambda]
  type        = "zip"
  source_file = "${path.module}/../../lambda-nuclei-scanner/bootstrap"
  output_path = "${path.module}/lambda.zip"
}

# ==============================================================================
# ARTIFACT VALIDATION
# ==============================================================================

# Validate that all required artifacts exist
resource "null_resource" "validate_artifacts" {
  depends_on = [
    aws_s3_object.upload_nuclei,
    aws_s3_object.upload_templates,
    aws_s3_object.upload_config,
    data.archive_file.lambda_zip
  ]

  triggers = {
    nuclei_version = var.nuclei_version
    lambda_hash    = data.archive_file.lambda_zip.output_md5
    templates_hash = data.archive_file.custom_nuclei_templates_zip.output_md5
    config_hash    = data.archive_file.nuclei_config.output_md5
  }

  provisioner "local-exec" {
    command = <<-EOT
      echo "Validating artifacts..."

      # Check if all S3 objects exist
      aws s3api head-object --bucket "${aws_s3_bucket.artifacts_bucket.id}" --key "nuclei.zip" > /dev/null
      aws s3api head-object --bucket "${aws_s3_bucket.artifacts_bucket.id}" --key "custom-nuclei-templates.zip" > /dev/null
      aws s3api head-object --bucket "${aws_s3_bucket.artifacts_bucket.id}" --key "nuclei-configs.zip" > /dev/null

      # Check if Lambda zip exists
      if [ ! -f "${path.module}/lambda.zip" ]; then
        echo "Error: Lambda function zip not found"
        exit 1
      fi

      echo "All artifacts validated successfully"
    EOT
  }
}
