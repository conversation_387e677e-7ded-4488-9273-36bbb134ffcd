# Network Module

This Terraform module provides shared networking infrastructure for the Nuclear Pond project, including VPC, subnets, internet gateway, NAT gateway, and route tables.

## Architecture

The module creates a complete networking foundation:

```
┌─────────────────────────────────────────────────────────────┐
│                        VPC (10.0.0.0/16)                   │
├─────────────────────────┬───────────────────────────────────┤
│     Public Subnets      │        Private Subnets            │
│                         │                                   │
│  ┌─────────────────┐   │   ┌─────────────────────────────┐ │
│  │ Public AZ1      │   │   │ Private AZ1                 │ │
│  │ ********/24     │   │   │ ********/24                 │ │
│  │ - ALB           │   │   │ - ECS Tasks                 │ │
│  │ - NAT Gateway   │   │   │ - Private Resources         │ │
│  └─────────────────┘   │   └─────────────────────────────┘ │
│                         │                                   │
│  ┌─────────────────┐   │   ┌─────────────────────────────┐ │
│  │ Public AZ2      │   │   │ Private AZ2                 │ │
│  │ ********/24     │   │   │ ********/24                 │ │
│  │ - ALB           │   │   │ - ECS Tasks                 │ │
│  └─────────────────┘   │   └─────────────────────────────┘ │
└─────────────────────────┴───────────────────────────────────┘
           │                              │
    Internet Gateway                 NAT Gateway
           │                              │
        Internet                     Internet (outbound)
```

## Features

- ✅ **Multi-AZ Design**: Resources distributed across two availability zones
- ✅ **Public/Private Separation**: Clear separation between public and private resources
- ✅ **Internet Access**: Internet Gateway for public subnets, NAT Gateway for private
- ✅ **Configurable CIDR**: Customizable IP address ranges
- ✅ **High Availability**: Optional second NAT Gateway for HA
- ✅ **Proper Routing**: Route tables configured for optimal traffic flow

## Usage

### Basic Usage

```hcl
module "network" {
  source = "./network"

  project_name = "fast-scan"
  tags = {
    Environment = "dev"
    Project     = "fast-scan"
  }
}
```

### Custom CIDR Blocks

```hcl
module "network" {
  source = "./network"

  project_name = "fast-scan"
  
  # Custom network configuration
  vpc_cidr                 = "**********/16"
  public_subnet_az1_cidr   = "**********/24"
  public_subnet_az2_cidr   = "**********/24"
  private_subnet_az1_cidr  = "***********/24"
  private_subnet_az2_cidr  = "***********/24"
  
  tags = {
    Environment = "prod"
    Project     = "fast-scan"
  }
}
```

### High Availability Configuration

```hcl
module "network" {
  source = "./network"

  project_name = "fast-scan"
  
  # Enable second NAT Gateway for HA
  enable_nat_gateway_ha = true
  
  tags = {
    Environment = "prod"
    Project     = "fast-scan"
  }
}
```

## Input Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| `project_name` | Name of the project used for resource naming | `string` | n/a | yes |
| `tags` | Common tags to apply to all resources | `map(string)` | `{}` | no |
| `vpc_cidr` | CIDR block for the VPC | `string` | `"10.0.0.0/16"` | no |
| `public_subnet_az1_cidr` | CIDR block for public subnet in AZ1 | `string` | `"********/24"` | no |
| `public_subnet_az2_cidr` | CIDR block for public subnet in AZ2 | `string` | `"********/24"` | no |
| `private_subnet_az1_cidr` | CIDR block for private subnet in AZ1 | `string` | `"********/24"` | no |
| `private_subnet_az2_cidr` | CIDR block for private subnet in AZ2 | `string` | `"********/24"` | no |
| `enable_nat_gateway_ha` | Whether to create a second NAT Gateway in AZ2 for high availability | `bool` | `false` | no |

## Outputs

| Name | Description |
|------|-------------|
| `vpc_id` | ID of the VPC |
| `vpc_cidr_block` | CIDR block of the VPC |
| `public_subnet_ids` | List of public subnet IDs |
| `private_subnet_ids` | List of private subnet IDs |
| `public_subnet_az1_id` | ID of public subnet in AZ1 |
| `public_subnet_az2_id` | ID of public subnet in AZ2 |
| `private_subnet_az1_id` | ID of private subnet in AZ1 |
| `private_subnet_az2_id` | ID of private subnet in AZ2 |
| `internet_gateway_id` | ID of the Internet Gateway |
| `nat_gateway_id` | ID of the NAT Gateway |
| `nat_gateway_public_ip` | Public IP of the NAT Gateway |
| `availability_zones` | List of availability zones used |

## Network Design Considerations

### Security

- **Private Subnets**: ECS tasks and sensitive resources run in private subnets
- **Public Subnets**: Only load balancers and NAT gateways in public subnets
- **No Direct Internet**: Private resources access internet through NAT Gateway

### High Availability

- **Multi-AZ**: Resources distributed across two availability zones
- **Optional HA NAT**: Second NAT Gateway can be enabled for production
- **Load Balancer**: ALB spans both public subnets

### Cost Optimization

- **Single NAT Gateway**: Default configuration uses one NAT Gateway to reduce costs
- **Right-sized Subnets**: /24 subnets provide adequate IP space without waste

## Integration with Other Modules

This network module is designed to be used by other modules in the project:

### Nuclear Pond Backend
### PoW Targets


## Troubleshooting

### Common Issues

1. **CIDR Conflicts**: Ensure CIDR blocks don't overlap with existing VPCs
2. **AZ Availability**: Some regions may not have all AZs available
3. **NAT Gateway Costs**: Monitor NAT Gateway data transfer costs

### Useful Commands

```bash
# Check VPC configuration
aws ec2 describe-vpcs --vpc-ids <vpc-id>

# Check subnet configuration
aws ec2 describe-subnets --filters "Name=vpc-id,Values=<vpc-id>"

# Check route tables
aws ec2 describe-route-tables --filters "Name=vpc-id,Values=<vpc-id>"

# Check NAT Gateway status
aws ec2 describe-nat-gateways --filter "Name=vpc-id,Values=<vpc-id>"
```

## Best Practices

1. **Use Consistent Naming**: Follow the project naming conventions
2. **Tag Everything**: Apply consistent tags for cost tracking and organization
3. **Plan CIDR Carefully**: Consider future growth and avoid conflicts
4. **Monitor Costs**: Keep an eye on NAT Gateway and data transfer costs
5. **Document Changes**: Update this README when making modifications

## Contributing

When modifying this module:

1. Update variable descriptions and defaults
2. Add appropriate tags to all resources
3. Update this documentation
4. Follow Terraform best practices
