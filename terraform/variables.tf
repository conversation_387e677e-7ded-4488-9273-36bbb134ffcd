variable "project_name" {
  description = "Name of the project to create and must be unique as S3 bucket names are global"
  default     = "fast-scan"
}

# Nuclei binary configuration
variable "nuclei_version" {
  description = "Nuclei version to use"
  default     = "3.1.7"
}

variable "nuclei_arch" {
  description = "Nuclei architecture to use"
  default     = "linux_amd64"
}

variable "nuclei_timeout" {
  type        = number
  description = "Lambda function timeout"
  default     = 900
}

variable "memory_size" {
  type        = number
  description = "Memory size for the Nuclei Lambda function in MB"
  default     = 512
}

variable "nuclei_lambda_environment" {
  description = "Deployment environment for the Nuclei Lambda module (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "tags" {
  type = map(string)
  default = {
    "Name" = "nuclei-scanner"
  }
}

variable "nuclearpond_api_key" {
  description = "API key for the Nuclear Pond service. This key will be passed as an environment variable to the ECS task."
  type        = string
  sensitive   = true # prevent output in console logs where possible.
}

# Proof of Work (PoW) target infrastructure variables
variable "enable_pow_targets" {
  description = "Whether to create PoW demo target infrastructure"
  type        = bool
  default     = false
}

variable "pow_domain_name" {
  description = "Domain name registered through Route53 to use for PoW targets (e.g., fast-scan-demo-target.click)"
  type        = string
  default     = "fast-scan-demo-target.click" # Default to the registered domain
}

# Frontend deployment variables
variable "enable_frontend_deployment" {
  description = "Whether to deploy the frontend infrastructure"
  type        = bool
  default     = true
}

variable "frontend_domain_name" {
  description = "Domain name for the frontend (e.g., app.fast-scan.com)"
  type        = string
  default     = ""
}

variable "create_frontend_route53_record" {
  description = "Whether to create Route53 record for the frontend"
  type        = bool
  default     = false
}

variable "frontend_route53_zone_id" {
  description = "Route53 hosted zone ID for the frontend domain"
  type        = string
  default     = ""
}

variable "enable_frontend_cloudfront" {
  description = "Whether to create CloudFront distribution for the frontend"
  type        = bool
  default     = true
}

variable "frontend_cloudfront_price_class" {
  description = "CloudFront price class (PriceClass_All, PriceClass_200, PriceClass_100)"
  type        = string
  default     = "PriceClass_100"
}

variable "frontend_environment" {
  description = "Deployment environment for the frontend (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "frontend_demo_password" {
  description = "Demo password for the frontend application"
  type        = string
  sensitive   = true
  default     = "TestPass"
}

# Network Infrastructure Variables
variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnet_az1_cidr" {
  description = "CIDR block for public subnet in AZ1"
  type        = string
  default     = "********/24"
}

variable "public_subnet_az2_cidr" {
  description = "CIDR block for public subnet in AZ2"
  type        = string
  default     = "********/24"
}

variable "private_subnet_az1_cidr" {
  description = "CIDR block for private subnet in AZ1"
  type        = string
  default     = "********/24"
}

variable "private_subnet_az2_cidr" {
  description = "CIDR block for private subnet in AZ2"
  type        = string
  default     = "********/24"
}

variable "enable_nat_gateway_ha" {
  description = "Whether to create a second NAT Gateway in AZ2 for high availability"
  type        = bool
  default     = false
}

# Nuclear Pond Backend Variables
variable "enable_nuclear_pond_backend" {
  description = "Whether to deploy the Nuclear Pond backend infrastructure"
  type        = bool
  default     = true
}

variable "nuclear_pond_environment" {
  description = "Deployment environment for the Nuclear Pond backend (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "nuclear_pond_task_cpu" {
  description = "CPU units for the Nuclear Pond ECS task (256, 512, 1024, etc.)"
  type        = string
  default     = "256"
}

variable "nuclear_pond_task_memory" {
  description = "Memory for the Nuclear Pond ECS task in MB (512, 1024, 2048, etc.)"
  type        = string
  default     = "512"
}

variable "nuclear_pond_desired_count" {
  description = "Desired number of Nuclear Pond ECS tasks"
  type        = number
  default     = 1
}

variable "nuclear_pond_container_port" {
  description = "Port on which the Nuclear Pond container listens"
  type        = number
  default     = 8082
}

variable "nuclear_pond_health_check_path" {
  description = "Path for ALB health checks for Nuclear Pond"
  type        = string
  default     = "/health-check"
}

variable "nuclear_pond_log_retention_days" {
  description = "Number of days to retain CloudWatch logs for Nuclear Pond"
  type        = number
  default     = 30
}

variable "nuclear_pond_enable_deletion_protection" {
  description = "Whether to enable deletion protection for the Nuclear Pond ALB"
  type        = bool
  default     = false
}