# ==============================================================================
# PROVIDER CONFIGURATIONS
# ==============================================================================

# Default AWS provider (uses the region from AWS CLI or environment variables)
# This is the primary provider for most resources
provider "aws" {
  region = "us-east-1"
}

# Additional AWS provider for us-east-1 region
# Required for CloudFront ACM certificates which must be in us-east-1
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}

# ==============================================================================
# NUCLEI LAMBDA INFRASTRUCTURE
# ==============================================================================

module "nuclei_lambda" {
  source = "./nuclei_lambda"

  # Core configuration
  project_name = var.project_name
  environment  = var.nuclei_lambda_environment

  # Nuclei configuration
  nuclei_version = var.nuclei_version
  nuclei_arch    = var.nuclei_arch
  nuclei_timeout = var.nuclei_timeout
  memory_size    = var.memory_size

  # Storage configuration
  custom_templates_path = "../nuclear_pond/templates/"
  nuclei_config_path    = "./config"

  # Monitoring configuration (basic for now)
  log_retention_days = 30

  tags = var.tags
}

###
# Shared Network Infrastructure
###
module "network" {
  source = "./network"

  project_name = var.project_name
  tags         = var.tags

  # Network configuration
  vpc_cidr                 = var.vpc_cidr
  public_subnet_az1_cidr   = var.public_subnet_az1_cidr
  public_subnet_az2_cidr   = var.public_subnet_az2_cidr
  private_subnet_az1_cidr  = var.private_subnet_az1_cidr
  private_subnet_az2_cidr  = var.private_subnet_az2_cidr
  enable_nat_gateway_ha    = var.enable_nat_gateway_ha
}

###
# Nuclear Pond Backend Infrastructure
###
module "nuclear_pond_backend" {
  source = "./nuclear_pond_backend"
  count  = var.enable_nuclear_pond_backend ? 1 : 0

  # Common parameters
  project_name = var.project_name
  environment  = var.nuclear_pond_environment
  tags         = var.tags

  # Network configuration
  vpc_id             = module.network.vpc_id
  public_subnet_ids  = module.network.public_subnet_ids
  private_subnet_ids = module.network.private_subnet_ids

  # Application configuration
  api_key                = var.nuclearpond_api_key
  lambda_function_name   = module.nuclei_lambda.lambda_function_name
  lambda_function_arn    = module.nuclei_lambda.lambda_function_arn
  dynamodb_table_name    = aws_dynamodb_table.scan_state_table.name
  dynamodb_table_arn     = aws_dynamodb_table.scan_state_table.arn

  # ECS configuration
  task_cpu                = var.nuclear_pond_task_cpu
  task_memory             = var.nuclear_pond_task_memory
  desired_count           = var.nuclear_pond_desired_count
  container_port          = var.nuclear_pond_container_port
  health_check_path       = var.nuclear_pond_health_check_path

  # Monitoring configuration
  log_retention_days      = var.nuclear_pond_log_retention_days

  # Load balancer configuration
  enable_deletion_protection = var.nuclear_pond_enable_deletion_protection
}

###
# Proof of Work (PoW) Target Infrastructure
###
module "pow_targets" {
  source = "./pow"

  # Only create PoW resources if enabled
  enable_pow_targets = var.enable_pow_targets

  # Common parameters
  project_name = var.project_name
  tags         = var.tags

  # Domain configuration
  pow_domain_name = var.pow_domain_name

  # Network configuration - use shared network module
  vpc_id            = module.network.vpc_id
  public_subnet_ids = module.network.public_subnet_ids
}

###
# Frontend Infrastructure
###
module "frontend" {
  source = "./frontend"
  count  = var.enable_frontend_deployment ? 1 : 0

  # Provider configuration for ACM certificate (must be in us-east-1 for CloudFront)
  providers = {
    aws.us_east_1 = aws.us_east_1
  }

  # Common parameters
  project_name = var.project_name
  tags         = var.tags
  environment  = var.frontend_environment

  # Domain configuration
  frontend_domain_name  = var.frontend_domain_name
  create_route53_record = var.create_frontend_route53_record
  route53_zone_id       = var.frontend_route53_zone_id

  # CloudFront configuration
  enable_cloudfront     = var.enable_frontend_cloudfront
  cloudfront_price_class = var.frontend_cloudfront_price_class

  # API configuration
  api_url       = var.enable_nuclear_pond_backend ? "http://${module.nuclear_pond_backend[0].alb_dns_name}" : ""
  api_key       = var.nuclearpond_api_key
  demo_password = var.frontend_demo_password
}