# Database Operations Reference

This document outlines the standardized database operations for the Nuclear Pond scanning system, providing a clean and consistent interface for managing scan data in DynamoDB.

## Overview

The Nuclear Pond system uses AWS DynamoDB to store and manage scan lifecycle data. All database operations follow a consistent pattern with proper error handling, logging, and data validation.

## Database Schema

### Primary Table: `scan_state_table`
- **Primary Key**: `scan_id` (String) - UUID without dashes for DynamoDB compatibility
- **TTL**: Automatic cleanup after 24 hours
- **Attributes**:
  - `scan_id`: Primary key (UUID without dashes)
  - `request_id`: Original UUID with dashes for API responses
  - `status`: Current scan status (`running`, `completed`, `failed`)
  - `targets`: List of target URLs/domains
  - `args`: Nuclei command-line arguments
  - `mode`: Execution mode (`local` or `cloud`)
  - `batches`: Number of target batches
  - `threads`: Number of concurrent threads
  - `output`: Output destination (`s3`, `local`)
  - `created_at`: ISO 8601 timestamp
  - `updated_at`: ISO 8601 timestamp
  - `completed_at`: ISO 8601 timestamp (optional)
  - `ttl`: Unix timestamp for automatic cleanup

## Database Operations

### 1. CREATE Operations (INSERT)

#### `storeScanRequest(scanRequest *ScanRequest) error`
**Purpose**: Creates a new scan record when a scan is initiated.

**Usage**:
```go
scanRequest := &ScanRequest{
    ScanID:    requestId,
    RequestID: scanId,
    Status:    "running",
    Targets:   targets,
    // ... other fields
}
err := storeScanRequest(scanRequest)
```

**Features**:
- Uses `PutItem` with condition to prevent overwrites
- Converts Go structs to DynamoDB format
- Handles optional fields (e.g., `completed_at`)
- Comprehensive error handling and logging

### 2. UPDATE Operations

#### `updateScanStatus(requestId string, status string) error`
**Purpose**: Updates scan status and timestamps during execution.

**Usage**:
```go
err := updateScanStatus(requestId, "completed")
```

**Features**:
- Updates `status` and `updated_at` fields
- Automatically sets `completed_at` for final statuses
- Uses conditional expressions for atomic updates

### 3. READ Operations (GET)

#### `getScanByID(scanId string) (*ScanRequest, error)`
**Purpose**: Retrieves a single scan record by ID.

**Usage**:
```go
scan, err := getScanByID(requestId)
if err != nil {
    // Handle not found or other errors
}
```

**Features**:
- Returns complete scan details
- Handles missing records gracefully
- Uses `parseScanFromDynamoDB` for consistent data transformation

### 4. LIST Operations (SCAN)

#### `getAllScans(limit int, exclusiveStartKey map[string]*dynamodb.AttributeValue, statusFilter string) (*ScanListResponse, error)`
**Purpose**: Retrieves multiple scans with filtering and pagination.

**Usage**:
```go
scans, err := getAllScans(20, nil, "running")
```

**Features**:
- Supports pagination with `exclusiveStartKey`
- Optional status filtering
- Configurable result limits
- Returns structured response with metadata

### 5. UTILITY Operations

#### `parseScanFromDynamoDB(item map[string]*dynamodb.AttributeValue) (*ScanRequest, error)`
**Purpose**: Converts DynamoDB items to Go structs consistently.

**Features**:
- Handles all data type conversions
- Graceful handling of missing or malformed fields
- Consistent error logging for debugging

## Error Handling Patterns

### 1. Session Management
```go
sess, err := session.NewSession(&aws.Config{
    Region: aws.String(os.Getenv("AWS_REGION")),
})
if err != nil {
    return fmt.Errorf("failed to create AWS session: %v", err)
}
```

### 2. Conditional Operations
```go
putInput := &dynamodb.PutItemInput{
    TableName: aws.String(os.Getenv("AWS_DYNAMODB_TABLE")),
    Item:      item,
    ConditionExpression: aws.String("attribute_not_exists(scan_id)"),
}
```

### 3. Graceful Degradation
```go
if err := storeScanRequest(scanRequest); err != nil {
    log.Printf("Failed to store scan request: %v", err)
    // Continue with execution even if storage fails
}
```

## Best Practices

1. **Separation of Concerns**: Each function has a single responsibility
2. **Consistent Logging**: All operations include structured logging
3. **Error Propagation**: Errors are properly wrapped with context
4. **Data Validation**: Input validation before database operations
5. **Resource Cleanup**: Proper session and resource management
6. **Atomic Operations**: Use conditional expressions for data consistency

## Integration Points

### API Handlers
- `scanHandler`: Uses `storeScanRequest` for new scans
- `scanStatusHandler`: Uses `getScanByID` for status retrieval
- `scansListHandler`: Uses `getAllScans` for listing scans
- `scanResultsHandler`: Uses `getScanByID` for results retrieval

### Background Processing
- `backgroundScan`: Uses `storeScanRequest` and `updateScanStatus`
- Local/Cloud execution: Uses `updateScanStatus` for status updates

## Environment Variables

Required environment variables for database operations:
- `AWS_REGION`: AWS region for DynamoDB
- `AWS_DYNAMODB_TABLE`: DynamoDB table name
- AWS credentials (via IAM roles or environment variables)

## Monitoring and Debugging

All database operations include comprehensive logging:
- Operation start/completion
- Error conditions with context
- Performance metrics (implicit via CloudWatch)
- Data validation warnings

For debugging, check CloudWatch logs for:
- `"Storing new scan request in DynamoDB"`
- `"Failed to store scan request"`
- `"Failed to parse scan from DynamoDB"`
- `"Failed to update scan status in DynamoDB"`
