# Nuclear Pond API Reference

This guide provides comprehensive documentation for the Nuclear Pond HTTP API server, including server setup, authentication, endpoints, request/response formats, and client usage examples.

## Overview

The Nuclear Pond API server provides HTTP endpoints for programmatic control over Nuclei scanning functionality. It acts as a pure orchestrator, delegating scan execution to lambda-nuclei-scanner in both local and cloud modes. The API uses AWS DynamoDB for persistent scan state management.

**Key Architecture Notes:**
- **Local Mode**: API server starts `nuclei_local_runner` subprocesses and communicates via API calls
- **Cloud Mode**: API server invokes AWS Lambda functions running lambda-nuclei-scanner
- **Unified Interface**: Both modes use the same lambda-nuclei-scanner execution engine for consistency

## API Server Setup

To use the API, you first need to start the Nuclear Pond API server using the `nuclearpond service` command.

### Starting the API Server

```bash
# Ensure the nuclearpond binary is built and accessible
# ./nuclearpond service [flags] # (No specific flags for 'service' command itself usually)
```

The server starts on port **8082** by default.

### Required Environment Variables for API Server

The API server requires specific environment variables to be set for its operation:

| Variable                   | Purpose                                                                 | Required | Notes                                                                                             |
| -------------------------- | ----------------------------------------------------------------------- | -------- | ------------------------------------------------------------------------------------------------- |
| `NUCLEARPOND_API_KEY`      | Secure API key for client authentication (Bearer Token).                | Yes      | Choose a strong, unique key.                                                                      |
| `AWS_DYNAMODB_TABLE`       | Name or ARN of the AWS DynamoDB table used for storing scan state.      | Yes      | This table tracks scan IDs, status, progress, etc. Must be deployed.                                |
| `AWS_REGION`               | AWS region where DynamoDB table and Lambda function (for cloud mode) reside. | Yes      | e.g., `us-east-1`                                                                                 |
| `AWS_LAMBDA_FUNCTION_NAME` | Name or ARN of the AWS Lambda function for cloud-mode scans via API.    | Yes, if using `cloud` mode via API | e.g., `my-nuclear-pond-lambda`                                                                    |
| `AWS_PROFILE`              | (Optional) AWS CLI profile to use for AWS credentials and configuration. | No       |                                                                                                   |

### Additional Requirements for Local Mode

For local mode API operations, ensure:
- The `nuclei_local_runner` executable is built and accessible to the API server
- Build it using: `cd lambda-nuclei-scanner && npm run build:standalone`
- The executable should be in the expected location relative to the API server

**Example Server Startup:**
```bash
export NUCLEARPOND_API_KEY="your-very-secure-api-key-here"
export AWS_DYNAMODB_TABLE="nuclear-pond-scan-state-table"
export AWS_REGION="us-west-2"
export AWS_LAMBDA_FUNCTION_NAME="nuclear-pond-lambda-scanner"

./nuclearpond service
# Server will log that it's listening on port 8082
```

### Base URL

-   **Local Development**: `http://localhost:8082`
-   **Production Deployment**: This will be your load balancer URL or service discovery endpoint (e.g., `https://api.nuclearpond.yourdomain.com`).

## Authentication

The API uses **Bearer Token** authentication. Clients must include the API key (specified by the `NUCLEARPOND_API_KEY` environment variable on the server) in the `Authorization` header of their HTTP requests.

```http
Authorization: Bearer your-very-secure-api-key-here
```

**Security Best Practices for API Keys:**
-   Store API keys securely on the client-side (e.g., environment variables, secret management systems).
-   Use HTTPS for all API communication in production environments.
-   Implement rate limiting and request monitoring on the API server (usually via an API Gateway or load balancer).
-   Consider rotating API keys periodically.

## Endpoints

### Health Check

Checks if the API server is running and healthy. Useful for monitoring and load balancer health checks.

-   **Endpoint**: `GET /health-check`
-   **Authentication**: None required.
-   **Description**: Returns the server's health status, current timestamp, and application version.

#### Request Example (cURL)
```bash
curl http://localhost:8082/health-check
```

#### Response Example (200 OK)
```json
{
  "status": "healthy",
  "timestamp": "2024-03-15T10:30:00Z",
  "version": "1.0.0" // Example version
}
```

**Possible Response Codes:**
-   `200 OK`: Server is healthy.
-   `503 Service Unavailable`: Server is experiencing issues (e.g., cannot connect to DynamoDB).

---

### Root/Index

Provides basic information about the API and available top-level endpoints.

-   **Endpoint**: `GET /`
-   **Authentication**: None required.
-   **Description**: Returns service name, version, and a list of primary endpoints.

#### Request Example (cURL)
```bash
curl http://localhost:8082/
```

#### Response Example (200 OK)
```json
{
  "service": "Nuclear Pond API",
  "version": "1.0.0", // Example version
  "documentation": "/docs/api-reference.md", // Or a link to hosted docs
  "endpoints": [
    "/health-check",
    "/scan",
    "/scan/{scan_id}"
  ]
}
```

---

### Start Scan

Initiates a new Nuclei scan with specified targets and parameters. The scan is processed asynchronously.

-   **Endpoint**: `POST /scan`
-   **Authentication**: Required (Bearer Token).
-   **Description**: Submits a new scan request. The server validates the request, assigns a unique `scan_id`, stores initial scan data in DynamoDB, and begins processing (either locally or by invoking cloud resources).

#### Request Body Format (JSON)

```json
{
  "targets": ["target1.example.com", "target2.example.com"], // Required, array of strings
  "args": ["LXQgZG5zIC1zZXZlcml0eSBoaWdo"], // Required, array of base64-encoded Nuclei argument strings
  "mode": "local", // Optional, "local" or "cloud". Default: "local"
  "options": { // Optional object for additional scan parameters
    "threads": 4, // For "local" mode: number of parallel threads. For "cloud" mode: number of concurrent Lambda invocations.
    "batch_size": 10, // Targets per thread/Lambda invocation.
    "output_format": "json", // For "cloud" mode with S3: "cmd", "json", "s3". Default behavior might vary.
    "templates_path": "/path/to/local/templates" // For "local" mode: path to custom templates (Nuclear Pond specific staging, see User Guide).
  }
}
```

#### Request Body Parameters

| Field                  | Type   | Required | Description                                                                                                                              |
| ---------------------- | ------ | -------- | ---------------------------------------------------------------------------------------------------------------------------------------- |
| `targets`              | array  | Yes      | List of target strings (domains, IPs, URLs) to scan.                                                                                     |
| `args`                 | array  | Yes      | List of base64-encoded strings, where each string represents a set of arguments for Nuclei (e.g., `["LXQgZG5z", "LXNldmVyaXR5IGNyaXRpY2Fs"]`). See User Guide for encoding. |
| `mode`                 | string | No       | Execution mode. `local` (default) runs scans on the API server machine. `cloud` triggers AWS Lambda functions.                          |
| `options`              | object | No       | Container for additional, mode-specific options.                                                                                         |
| `options.threads`      | number | No       | Concurrency level. For `local` mode, it's local processes. For `cloud` mode, it's Lambda concurrency. Default typically `1` or `4`. Check server implementation. |
| `options.batch_size`   | number | No       | Number of targets processed per thread or Lambda function. Default typically `1`.                                                          |
| `options.output_format`| string | No       | For `cloud` mode, specifies how results are handled by Lambda: `cmd` (logs), `json` (logs as JSON), or `s3` (output to S3). Details in User Guide. |
| `options.templates_path`| string | No       | For `local` mode, a Nuclear Pond specific path to a directory of custom templates to stage. Not Nuclei's `-tp` arg. See User Guide.        |

#### Example Request (cURL - Local Scan)
```bash
API_KEY="your-very-secure-api-key-here"
NUCLEI_ARGS_B64=$(echo -ne "-t dns -severity high,critical" | base64)

curl -X POST http://localhost:8082/scan \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "targets": ["example.com", "test.com"],
    "args": ["'$NUCLEI_ARGS_B64'"],
    "mode": "local",
    "options": {
      "threads": 8,
      "batch_size": 1
    }
  }'
```

#### Example Request (cURL - Cloud Scan to S3)
```bash
API_KEY="your-very-secure-api-key-here"
NUCLEI_ARGS_B64=$(echo -ne "-t cves -o results.json -oj" | base64) # Lambda should be configured to pick up -o

curl -X POST http://localhost:8082/scan \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "targets": ["largescope.example.com"],
    "args": ["'$NUCLEI_ARGS_B64'"],
    "mode": "cloud",
    "options": {
      "threads": 50,      // 50 concurrent Lambdas
      "batch_size": 20,   // 20 targets per Lambda
      "output_format": "s3" // Instruct Lambda to send output to S3
    }
  }'
```

#### Response (202 Accepted)
Indicates the scan request was accepted and is being processed. The `scan_id` is crucial for polling status.
```json
{
  "scan_id": "scan_123e4567-e89b-12d3-a456-426614174000",
  "status": "queued", // Or "starting"
  "message": "Scan accepted and queued for processing.",
  "targets_count": 2,
  "mode": "local",
  "created_at": "2024-03-15T10:35:00Z"
  // "estimated_duration" might be too complex to predict accurately here.
}
```

#### Error Responses
-   `400 Bad Request`: Invalid request payload (e.g., missing `targets` or `args`, invalid JSON).
    ```json
    {
      "error": "Invalid request payload",
      "message": "Field 'targets' is required and must be an array of strings.",
      "code": "INVALID_REQUEST_PAYLOAD"
    }
    ```
-   `401 Unauthorized`: Missing or invalid API key.
    ```json
    {
      "error": "Unauthorized",
      "message": "Authentication token is missing or invalid.",
      "code": "UNAUTHORIZED"
    }
    ```
-   `500 Internal Server Error`: Server-side issue (e.g., failed to write to DynamoDB).
    ```json
    {
      "error": "Internal server error",
      "message": "Failed to initialize scan due to a database error.",
      "code": "INTERNAL_ERROR"
    }
    ```

---

### Get Scan Status & Results

Retrieve the current status, progress, and results (or result location) of a previously submitted scan.

-   **Endpoint**: `GET /scan/{scan_id}`
-   **Authentication**: Required (Bearer Token).
-   **Path Parameter**: `scan_id` (string) - The ID received from the `POST /scan` response.
-   **Description**: Fetches the scan's current state from DynamoDB.

#### Request Example (cURL)
```bash
API_KEY="your-very-secure-api-key-here"
SCAN_ID="scan_123e4567-e89b-12d3-a456-426614174000"

curl http://localhost:8082/scan/$SCAN_ID \
  -H "Authorization: Bearer $API_KEY"
```

#### Response Examples

**Scan in Progress (Running):**
```json
{
  "scan_id": "scan_123e4567-e89b-12d3-a456-426614174000",
  "status": "running",
  "mode": "cloud",
  "targets_count": 100,
  "progress": {
    "completed_targets": 45,
    "total_targets": 100,
    "percentage_complete": 45.0
  },
  "created_at": "2024-03-15T10:35:00Z",
  "updated_at": "2024-03-15T10:38:15Z"
  // "estimated_completion_time" could be added if calculable
}
```

**Scan Completed Successfully:**
```json
{
  "scan_id": "scan_123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "mode": "local",
  "targets_count": 2,
  "progress": {
    "completed_targets": 2,
    "total_targets": 2,
    "percentage_complete": 100.0
  },
  "results_summary": {
    "findings_count": 5,
    "severities": {
      "critical": 1,
      "high": 2,
      "medium": 2,
      "low": 0,
      "info": 0
    }
    // Actual findings are usually too verbose for summary; direct findings or S3 links are better.
  },
  // For cloud scans with S3 output:
  // "results_location": "s3://your-nuclearpond-results-bucket/scan_123e4567-e89b-12d3-a456-426614174000/",
  // For local scans, results might be directly inlined if small, or a path on the server if configured.
  // "findings": [ ... array of actual finding objects ... ] // Potentially large!
  "created_at": "2024-03-15T10:35:00Z",
  "updated_at": "2024-03-15T10:40:22Z",
  "completed_at": "2024-03-15T10:40:22Z",
  "duration_seconds": 322
}
```

**Scan Failed:**
```json
{
  "scan_id": "scan_123e4567-e89b-12d3-a456-426614174000",
  "status": "failed",
  "mode": "cloud",
  "error_info": {
    "message": "One or more Lambda invocations failed or timed out.",
    "code": "CLOUD_EXECUTION_FAILURE",
    "details": "Check CloudWatch logs for Lambda function: nuclear-pond-lambda-scanner for errors related to this scan ID."
  },
  "created_at": "2024-03-15T10:35:00Z",
  "updated_at": "2024-03-15T10:45:10Z",
  "failed_at": "2024-03-15T10:45:10Z"
}
```

#### Error Responses
-   `401 Unauthorized`: Missing or invalid API key.
-   `404 Not Found`: The specified `scan_id` does not exist in DynamoDB.
    ```json
    {
      "error": "Scan not found",
      "message": "No scan exists with the ID: scan_invalid-id-123",
      "code": "NOT_FOUND"
    }
    ```
-   `500 Internal Server Error`: Server-side issue (e.g., failed to read from DynamoDB).

## Scan Status Values

The `status` field in scan objects can have the following values:

| Status      | Description                                                              |
| ----------- | ------------------------------------------------------------------------ |
| `queued`    | Scan request accepted and waiting for processing resources.              |
| `starting`  | Scan is being initialized (e.g., resources being provisioned).         |
| `running`   | Scan is actively being executed.                                         |
| `completed` | Scan finished successfully. Results (or links) should be available.    |
| `failed`    | Scan terminated due to an error. Check `error_info` for details.       |
| `cancelled` | Scan was cancelled by user/admin action (if cancellation endpoint exists). |

## API Error Codes

Common error codes returned in the `code` field of error responses:

| Code                        | Description                                                     |
| --------------------------- | --------------------------------------------------------------- |
| `INVALID_REQUEST_PAYLOAD`   | JSON format is invalid, or required fields are missing/wrong type. |
| `UNAUTHORIZED`              | API key missing, invalid, or expired.                           |
| `FORBIDDEN`                 | API key is valid, but lacks permission for the action.        |
| `NOT_FOUND`                 | Requested resource (e.g., scan ID) does not exist.              |
| `SCAN_INIT_FAILURE`         | Failed to initialize a new scan (e.g., DB write error).         |
| `LOCAL_EXECUTION_ERROR`     | Error occurred during a local mode scan execution (e.g., subprocess failure). |
| `CLOUD_EXECUTION_FAILURE`   | Error occurred during a cloud mode scan (e.g., Lambda failure). |
| `SUBPROCESS_ERROR`          | Error starting or communicating with nuclei_local_runner subprocess. |
| `RESOURCE_UNAVAILABLE`      | A required downstream resource (e.g., DynamoDB) is unavailable. |
| `INTERNAL_ERROR`            | A generic, unexpected server-side error occurred.               |
| `OPERATION_TIMEOUT`         | The requested operation timed out on the server-side.           |

## Client Examples

Refer to the original attached `api-reference.md` for Python, JavaScript, and Bash client examples. They provide a good foundation for interacting with these endpoints.

**Key considerations for clients:**
-   Implement polling logic for `GET /scan/{scan_id}` to monitor scan completion.
-   Use appropriate poll intervals (e.g., 15-60 seconds) to avoid overwhelming the API.
-   Handle different scan statuses and error codes gracefully.
-   Securely manage the API key.

## Rate Limiting & Best Practices (Server-Side Considerations)

While client behavior is important, the server should ideally implement its own rate limiting (e.g., per API key or source IP) using tools like API Gateways or middleware to protect against abuse and ensure fair usage.

-   **Client-Side Polling**: Clients should use reasonable polling intervals (e.g., 30s, 60s, increasing with scan duration) when checking `/scan/{scan_id}`.
-   **Error Handling**: Clients should robustly handle HTTP status codes (4xx, 5xx) and the API error codes in responses.
-   **Idempotency**: For `POST /scan`, consider if an idempotency key mechanism is needed if clients might retry on transient network errors, to avoid creating duplicate scans. 