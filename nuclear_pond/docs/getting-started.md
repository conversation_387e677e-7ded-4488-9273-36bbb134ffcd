# Getting Started with Nuclear Pond

This guide walks you through the essential steps to install Nuclear Pond, set up the execution engine, and run your first basic scan locally.

For advanced usage, cloud deployment, or API integration, please refer to the main [Documentation Hub](README.md).

## Prerequisites

Before installing Nuclear Pond, ensure you have the following:

-   **Go (Golang)**: Version 1.16 or later. Needed to build Nuclear Pond from source.
    -   *Installation*: Visit [golang.org/dl/](https://golang.org/dl/)
-   **Node.js and npm**: Required to build the lambda-nuclei-scanner execution engine.
    -   *Installation*: Visit [nodejs.org](https://nodejs.org/)
-   **Git**: Required for cloning the repository.

Optional (for specific features, covered in other guides):
-   AWS Account & CLI: For cloud execution.
-   Docker: For containerized deployments of the API server (covered in the [Deployment Guide](../../terraform/nuclear_pond_backend/DEPLOYMENT.md)).

## Installation

1.  **Build the Nuclear Pond Server**:
    Inside the `nuclear_pond` directory, build the executable:
    ```bash
    cd nuclear_pond
    go build -o nuclearpond
    ```
    Verify the build by checking for the `nuclearpond` executable in the current directory.

3.  **Build the Lambda-Nuclei-Scanner Execution Engine**:
    Build the `nuclei_local_runner` executable that Nuclear Pond uses for local execution:
    ```bash
    cd ../lambda-nuclei-scanner
    npm install
    npm run build:standalone
    ```
    This creates the `nuclei_local_runner` executable that Nuclear Pond will use as a subprocess for local scans.

## Installing Nuclei Engine

The lambda-nuclei-scanner execution engine requires Nuclei to be installed and accessible. Since Nuclear Pond now uses lambda-nuclei-scanner as a subprocess, Nuclei needs to be available to that subprocess.

### Automated Installation (Recommended)

If your project includes a helper script, you can use it to install Nuclei:

```bash
# If using workspace-level yarn scripts (from the fastscan root)
cd .. # Go to workspace root if you're in nuclear_pond
yarn install:nuclei
cd nuclear_pond # Return to nuclear_pond directory
```

This typically downloads Nuclei to a local `./bin` directory in the workspace.

### Manual Installation

1.  **Download Nuclei**:
    Go to the [Nuclei releases page](https://github.com/projectdiscovery/nuclei/releases) and download the binary for your OS and architecture.

2.  **Install Nuclei**:
    Extract and move the `nuclei` binary to a directory in your system's PATH (e.g., `/usr/local/bin` on Linux/macOS) or place it in a known location accessible to lambda-nuclei-scanner.
    ```bash
    # Example for Linux (global installation):
    curl -LO https://github.com/projectdiscovery/nuclei/releases/download/vX.Y.Z/nuclei_X.Y.Z_linux_amd64.zip
    unzip nuclei_X.Y.Z_linux_amd64.zip
    sudo mv nuclei /usr/local/bin/
    chmod +x /usr/local/bin/nuclei
    ```

### Verify Nuclei Installation

Open a new terminal window and run:
```bash
# If installed globally in PATH
nuclei -version

# If installed to a local ./bin directory (e.g., by yarn script)
# From the workspace root:
./bin/nuclei -version
```

**Note**: The lambda-nuclei-scanner subprocess will automatically detect and use Nuclei from the system PATH or common installation locations.

## Your First Local Scan

With Nuclear Pond, lambda-nuclei-scanner, and Nuclei installed, you can run a simple local scan.

1.  **Ensure executables are ready**:
    ```bash
    # Make sure nuclearpond is executable
    cd nuclear_pond
    chmod +x ./nuclearpond
    
    # Verify nuclei_local_runner was built
    ls -la ../lambda-nuclei-scanner/nuclei_local_runner
    ```

2.  **Run a Scan**:
    Let's scan `example.com` using Nuclei's built-in `dns` templates. Nuclei arguments need to be base64 encoded for the `-a` flag.

    First, encode the Nuclei arguments. For example, to use DNS templates (`-t dns`):
    ```bash
    echo -ne "-t dns" | base64
    ```
    This will output a base64 string, for example: `LXQgZG5z`

    Now, use this encoded string with Nuclear Pond:
    ```bash
    # Run the scan (Nuclear Pond will use nuclei_local_runner as subprocess)
    ./nuclearpond local -t example.com -a LXQgZG5z
    ```

    This command tells Nuclear Pond to:
    1. Start a `nuclei_local_runner` subprocess
    2. Send the target and arguments to the subprocess via API
    3. The subprocess runs Nuclei with the `-t dns` arguments against `example.com`
    4. Results are returned and displayed

    You should see output from the subprocess indicating the scan progress and any findings.

## Troubleshooting Common Issues

-   **`nuclearpond: command not found`**:
    -   Ensure `nuclearpond` binary is in your current directory (use `./nuclearpond`) or in a directory listed in your system's PATH.
    -   Verify it was built correctly.
-   **`nuclei_local_runner: command not found` or subprocess errors**:
    -   Make sure you built the lambda-nuclei-scanner: `cd lambda-nuclei-scanner && npm run build:standalone`
    -   Verify the executable exists: `ls -la lambda-nuclei-scanner/nuclei_local_runner`
    -   Ensure it has execute permissions: `chmod +x lambda-nuclei-scanner/nuclei_local_runner`
-   **`nuclei: command not found` (during subprocess execution)**:
    -   Make sure Nuclei is installed and accessible via the system PATH.
    -   Run `nuclei -version` to confirm.
    -   The lambda-nuclei-scanner subprocess needs to find Nuclei in the PATH.
-   **Permission Denied (for executables)**:
    -   Use `chmod +x ./nuclearpond` and `chmod +x lambda-nuclei-scanner/nuclei_local_runner`.
-   **Base64 Encoding Issues**:
    -   Always use `echo -ne "<nuclei_args>" | base64`. The `-n` flag prevents adding a trailing newline, which is important.
    -   To verify: `echo "LXQgZG5z" | base64 -d` should output `-t dns`.
-   **Subprocess Communication Errors**:
    -   Ensure Nuclear Pond can find the `nuclei_local_runner` executable in the expected location.
    -   Check that both Nuclear Pond and lambda-nuclei-scanner are built for the same architecture.

## Next Steps

Congratulations on running your first scan with Nuclear Pond!

-   To learn about all CLI commands, advanced configurations (parallelism, templates, outputs), and different execution modes: See the **[User Guide (CLI)](user-guide.md)**.
-   For deploying Nuclear Pond to AWS: Consult the **[Deployment Guide (AWS)](../../terraform/nuclear_pond_backend/DEPLOYMENT.md)**.
-   To integrate with the API: Refer to the **[API Reference](api-reference.md)**.
-   To understand the system design: Read the **[Architecture Overview](architecture.md)**. 