#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync } = require('child_process');
const axios = require('axios');
const os = require('os');

// Configuration
const NUCLEI_VERSION = '3.1.7';
const NUCLEI_DIR = path.join(__dirname, '..', 'bin');

// Determine platform and architecture
function getPlatformInfo() {
  const platform = os.platform();
  const arch = os.arch();
  
  let osType;
  let archType;
  
  // Map OS
  if (platform === 'darwin') osType = 'macOS';
  else if (platform === 'linux') osType = 'linux';
  else if (platform === 'win32') osType = 'windows';
  else throw new Error(`Unsupported platform: ${platform}`);
  
  // Map architecture
  if (arch === 'x64') archType = 'amd64';
  else if (arch === 'arm64') archType = 'arm64';
  else if (arch === 'ia32') archType = '386';
  else throw new Error(`Unsupported architecture: ${arch}`);
  
  return { osType, archType };
}

// Function to download a file
async function downloadFile(url, filePath) {
  try {
    console.log(`Downloading from ${url}...`);
    const response = await axios({
      method: 'GET',
      url: url,
      responseType: 'stream',
    });

    const writer = fs.createWriteStream(filePath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
    });
  } catch (error) {
    console.error(`Error downloading ${url}: ${error.message}`);
    throw error;
  }
}

// Function to extract zip file
function extractZip(zipPath, destDir) {
  const platform = os.platform();
  
  try {
    if (platform === 'win32') {
      // For Windows, use PowerShell to extract
      execSync(`powershell -command "Expand-Archive -Path '${zipPath}' -DestinationPath '${destDir}' -Force"`, { stdio: 'inherit' });
    } else {
      // For Linux/macOS, use unzip
      execSync(`unzip -o "${zipPath}" -d "${destDir}"`, { stdio: 'inherit' });
    }
    console.log('Extraction completed successfully');
  } catch (error) {
    console.error(`Error extracting zip: ${error.message}`);
    throw error;
  }
}

// Make file executable (for Unix-like systems)
function makeExecutable(filePath) {
  const platform = os.platform();
  if (platform !== 'win32') {
    try {
      execSync(`chmod +x "${filePath}"`, { stdio: 'inherit' });
      console.log(`Made ${filePath} executable`);
    } catch (error) {
      console.error(`Error making file executable: ${error.message}`);
      throw error;
    }
  }
}

// Main function
async function main() {
  try {
    console.log('Starting nuclei installation...');
    
    // Get platform info
    const { osType, archType } = getPlatformInfo();
    console.log(`Detected platform: ${osType}, architecture: ${archType}`);
    
    // Create bin directory if it doesn't exist
    if (!fs.existsSync(NUCLEI_DIR)) {
      fs.mkdirSync(NUCLEI_DIR, { recursive: true });
      console.log(`Created directory: ${NUCLEI_DIR}`);
    }
    
    // Construct download URL
    let downloadUrl;
    let zipFileName;
    let nucleiExeName;
    
    if (osType === 'windows') {
      zipFileName = `nuclei_${NUCLEI_VERSION}_${osType}_${archType}.zip`;
      nucleiExeName = 'nuclei.exe';
    } else {
      zipFileName = `nuclei_${NUCLEI_VERSION}_${osType}_${archType}.zip`;
      nucleiExeName = 'nuclei';
    }
    
    downloadUrl = `https://github.com/projectdiscovery/nuclei/releases/download/v${NUCLEI_VERSION}/${zipFileName}`;
    const zipFilePath = path.join(NUCLEI_DIR, zipFileName);
    
    // Download nuclei zip
    await downloadFile(downloadUrl, zipFilePath);
    console.log(`Downloaded nuclei to ${zipFilePath}`);
    
    // Extract zip
    extractZip(zipFilePath, NUCLEI_DIR);
    
    // Make nuclei executable (for Unix-like systems)
    const nucleiExePath = path.join(NUCLEI_DIR, nucleiExeName);
    makeExecutable(nucleiExePath);
    
    // Clean up zip file
    fs.unlinkSync(zipFilePath);
    console.log(`Removed zip file: ${zipFilePath}`);
    
    console.log(`Nuclei v${NUCLEI_VERSION} installed successfully at ${nucleiExePath}`);
    console.log('You can now run nuclei using the "yarn install:nuclei" command');
    
  } catch (error) {
    console.error('Installation failed:', error.message);
    process.exit(1);
  }
}

// Run the main function
main();


// export PATH="/workspaces/fastscan/nuclear_pond/bin:$PATH"