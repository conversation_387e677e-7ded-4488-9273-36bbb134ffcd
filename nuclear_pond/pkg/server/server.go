package server

import (
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/render"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
)

type Request struct {
	Targets []string `json:"Targets"`
	Batches int      `json:"Batches"`
	Threads int      `json:"Threads"`
	Args    string   `json:"Args"`
	Output  string   `json:"Output"`
	Mode    string   `json:"Mode,omitempty"` // Optional field to explicitly specify local/cloud mode
}

type Response struct {
	RequestId string `json:"RequestId,omitempty"`
	Status    string `json:"status,omitempty"`
	Error     string `json:"error,omitempty"`
	Message   string `json:"message,omitempty"`
}

// CORS middleware using Chi
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.<PERSON><PERSON>().Set("Access-Control-Allow-Origin", "*")
		w.<PERSON><PERSON>().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, X-NuclearPond-API-Key")
		w.Header().Set("Access-Control-Max-Age", "86400") // 24 hours

		// Handle preflight OPTIONS request
		if r.Method == "OPTIONS" {
			log.Printf("CORS preflight request received for %s from origin: %s", r.URL.Path, r.Header.Get("Origin"))
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// API key authentication middleware
func apiKeyMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Skip API key check for health endpoint
		if r.URL.Path == "/health-check" {
			next.ServeHTTP(w, r)
			return
		}

		if !checkAPIKey(r) {
			log.Printf("Invalid API key for request %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, Response{
				Error:   "Unauthorized",
				Message: "Invalid or missing API key",
			})
			return
		}

		next.ServeHTTP(w, r)
	})
}

// Index handler
func indexHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Index request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

	render.JSON(w, r, Response{
		Message: "Welcome to the Nuclear Pond API",
	})
}

// Health check handler
func healthHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Health check request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

	render.JSON(w, r, Response{
		Status: "ok",
	})
}

// Scan status handler - enhanced with comprehensive data
func scanStatusHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Scan status request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

	scanId := chi.URLParam(r, "scanId")
	if scanId == "" {
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, Response{
			Error:   "Bad Request",
			Message: "Missing scan ID parameter",
		})
		return
	}

	log.Printf("Getting scan details for: %s", scanId)

	// Convert scanId to DynamoDB key format
	requestId := strings.ReplaceAll(scanId, "-", "")

	scanRequest, err := getScanByID(requestId)
	if err != nil {
		log.Printf("Error getting scan details for %s: %v", scanId, err)
		render.Status(r, http.StatusNotFound)
		render.JSON(w, r, Response{
			Error:   "Not Found",
			Message: "Scan not found: " + err.Error(),
		})
		return
	}

	// Format response
	response := ScanStatusResponse{
		ScanID:      scanRequest.ScanID,
		RequestID:   scanRequest.RequestID,
		Status:      scanRequest.Status,
		CreatedAt:   scanRequest.CreatedAt,
		UpdatedAt:   scanRequest.UpdatedAt,
		CompletedAt: scanRequest.CompletedAt,
		Config: ScanConfig{
			TargetCount: scanRequest.TargetCount,
			Targets:     []string{}, // Empty by default for performance
			Batches:     scanRequest.Batches,
			Threads:     scanRequest.Threads,
			Output:      scanRequest.Output,
			Mode:        scanRequest.Mode,
			Args:        scanRequest.Args,
		},
	}

	// Optionally populate actual targets if requested (could add query param for this)
	includeTargets := r.URL.Query().Get("include_targets")
	if includeTargets == "true" {
		if targets, err := populateTargetsFromS3(scanRequest); err == nil {
			response.Config.Targets = targets
		} else {
			log.Printf("Warning: Could not populate targets for scan %s: %v", scanId, err)
		}
	}

	render.JSON(w, r, response)
}

// List all scans handler
func scansListHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Scans list request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

	// Parse query parameters
	limitStr := r.URL.Query().Get("limit")
	statusFilter := r.URL.Query().Get("status")

	limit := 20 // Default limit
	if limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 && parsedLimit <= 100 {
			limit = parsedLimit
		}
	}

	if statusFilter == "" {
		statusFilter = "all"
	}

	// Get scans from DynamoDB
	scansList, err := getAllScans(limit, nil, statusFilter)
	if err != nil {
		log.Printf("Error getting scans list: %v", err)
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, Response{
			Error:   "Internal Server Error",
			Message: "Error retrieving scans: " + err.Error(),
		})
		return
	}

	render.JSON(w, r, scansList)
}

// Scan results handler (placeholder for future implementation)
func scanResultsHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Scan results request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

	scanId := chi.URLParam(r, "scanId")
	if scanId == "" {
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, Response{
			Error:   "Bad Request",
			Message: "Missing scan ID parameter",
		})
		return
	}

	// Convert scanId to DynamoDB key format
	requestId := strings.ReplaceAll(scanId, "-", "")

	scanRequest, err := getScanByID(requestId)
	if err != nil {
		log.Printf("Error getting scan details for %s: %v", scanId, err)
		render.Status(r, http.StatusNotFound)
		render.JSON(w, r, Response{
			Error:   "Not Found",
			Message: "Scan not found: " + err.Error(),
		})
		return
	}

	// TODO: Implement actual results retrieval from S3 or other storage
	response := ScanResultsResponse{
		ScanID:    scanRequest.ScanID,
		RequestID: scanRequest.RequestID,
		Status:    scanRequest.Status,
		Config: ScanConfig{
			TargetCount: scanRequest.TargetCount,
			Targets:     []string{}, // Empty by default for performance
			Batches:     scanRequest.Batches,
			Threads:     scanRequest.Threads,
			Output:      scanRequest.Output,
			Mode:        scanRequest.Mode,
			Args:        scanRequest.Args,
		},
		CreatedAt:   scanRequest.CreatedAt,
		CompletedAt: scanRequest.CompletedAt,
		// TODO: Add actual results summary and findings
		ResultsSummary:  nil,
		ResultsLocation: "",
		Findings:        []interface{}{},
	}

	// Optionally populate actual targets if requested
	includeTargets := r.URL.Query().Get("include_targets")
	if includeTargets == "true" {
		if targets, err := populateTargetsFromS3(scanRequest); err == nil {
			response.Config.Targets = targets
		} else {
			log.Printf("Warning: Could not populate targets for scan %s: %v", scanId, err)
		}
	}

	render.JSON(w, r, response)
}

// Scan handler
func scanHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Scan request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
	log.Printf("Request headers: %v", r.Header)

	var req Request
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		log.Printf("Error decoding JSON from %s: %v", r.RemoteAddr, err)
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, Response{
			Error:   "Bad Request",
			Message: "Error decoding JSON: " + err.Error(),
		})
		return
	}

	log.Printf("Received scan request: %+v", req)

	// Validate request
	if len(req.Targets) == 0 {
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, Response{
			Error:   "Bad Request",
			Message: "Targets field is required and must contain at least one target",
		})
		return
	}

	scanId := uuid.New().String()
	go backgroundScan(req, scanId)

	log.Printf("Started background scan with ID: %s", scanId)

	render.Status(r, http.StatusAccepted)
	render.JSON(w, r, Response{
		RequestId: scanId,
		Message:   "Scan request accepted and queued for processing",
	})
}

// Server configuration check
func serverCheck() {
	requiredEnvVars := map[string]string{
		"NUCLEARPOND_API_KEY":      "API key for authentication",
		"AWS_REGION":               "AWS region for Lambda execution",
		"AWS_LAMBDA_FUNCTION_NAME": "Lambda function name for Nuclei execution",
		"AWS_DYNAMODB_TABLE":       "DynamoDB table for scan tracking",
		"AWS_S3_BUCKET":            "S3 bucket for target and result storage",
	}

	for envVar, description := range requiredEnvVars {
		if value := os.Getenv(envVar); value == "" {
			log.Fatalf("CRITICAL: %s environment variable must be set (%s)", envVar, description)
		} else {
			log.Printf("✓ %s configured", envVar)
		}
	}
}

// Main server setup and routing
func HandleRequests() {
	// Load environment variables from .env file
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found or error loading .env file:", err)
	}

	// Check if the server is configured correctly
	serverCheck()

	log.Printf("Starting Nuclear Pond server with API key configured: %t", os.Getenv("NUCLEARPOND_API_KEY") != "")

	// Create Chi router
	r := chi.NewRouter()

	// Global middleware
	r.Use(middleware.Logger)
	r.Use(middleware.Recoverer)
	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(corsMiddleware)

	// Routes with API key protection (except health check)
	r.Route("/", func(r chi.Router) {
		r.Use(apiKeyMiddleware)
		r.Get("/", indexHandler)
		r.Post("/scan", scanHandler)
		r.Get("/scan/{scanId}", scanStatusHandler)
		r.Get("/scan/{scanId}/results", scanResultsHandler)
		r.Get("/scans", scansListHandler)
	})

	// Public health check endpoint (no API key required)
	r.Get("/health-check", healthHandler)

	log.Println("Nuclear Pond server starting on port 8082...")
	log.Println("CORS enabled for all origins")
	log.Println("API key authentication enabled for protected endpoints")

	if err := http.ListenAndServe(":8082", r); err != nil {
		log.Fatal("Server failed to start:", err)
	}
}
