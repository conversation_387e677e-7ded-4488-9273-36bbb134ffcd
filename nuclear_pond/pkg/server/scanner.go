package server

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"nuclear_pond/pkg/core"
	"nuclear_pond/pkg/helpers"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/dynamodb"
	"github.com/aws/aws-sdk-go/service/s3"
)

func backgroundScan(scanInput Request, scanId string) {
	targets := helpers.RemoveEmpty(scanInput.Targets)
	batches := helpers.SplitSlice(targets, scanInput.Batches)
	output := scanInput.Output
	threads := scanInput.Threads
	NucleiArgs := base64.StdEncoding.EncodeToString([]byte(scanInput.Args))
	silent := true

	// Convert scanId to a valid DynamoDB key
	requestId := strings.ReplaceAll(scanId, "-", "")

	log.Printf("Initiating scan with the id of %s with %d targets", scanId, len(targets))

	// Create comprehensive scan request record
	now := time.Now()
	scanRequest := &ScanRequest{
		ScanID:      requestId,
		RequestID:   scanId, // Original UUID with dashes
		Status:      "running",
		TargetCount: len(targets), // Store count of targets instead of the targets themselves
		Args:        scanInput.Args,
		Mode:        scanInput.Mode,
		Batches:     scanInput.Batches,
		Threads:     scanInput.Threads,
		Output:      scanInput.Output,
		CreatedAt:   now,
		UpdatedAt:   now,
		TTL:         now.Add(time.Duration(24 * time.Hour)).Unix(), // 24 hour TTL
	}

	// Always store targets in S3 for consistency and scalability
	log.Printf("Storing %d targets in S3 for scan %s", len(targets), scanId)
	targetsS3URL, err := storeTargetsInS3(targets, requestId)
	if err != nil {
		log.Printf("Failed to store targets in S3: %v", err)
		updateScanStatus(requestId, "failed")
		return
	}

	// Store S3 reference in scan request
	scanRequest.TargetsS3URL = targetsS3URL

	// Store comprehensive scan request in DynamoDB
	if err := storeScanRequest(scanRequest); err != nil {
		log.Printf("Failed to store scan request: %v", err)
		// If we can't store the initial request, we shouldn't proceed
		// Try to update the status to failed if the record somehow exists
		updateScanStatus(requestId, "failed")
		return
	}

	// Use original targets list for scanning (from memory)
	actualTargets := targets

	// Re-create batches with actual targets
	batches = helpers.SplitSlice(actualTargets, scanInput.Batches)

	log.Printf("Target distribution validation:")
	totalDistributed := 0
	for i, batch := range batches {
		log.Printf("Batch %d: %d targets", i+1, len(batch))
		totalDistributed += len(batch)
		// Log first few targets in each batch for verification
		for j, target := range batch {
			if j < 3 { // Log first 3 targets per batch
				log.Printf("  Batch %d Target %d: %s", i+1, j+1, target)
			}
		}
	}
	log.Printf("Total targets distributed: %d (expected: %d)", totalDistributed, len(actualTargets))

	// Check if this is a local mode request
	localMode := scanInput.Mode == "local" ||
		os.Getenv("NUCLEARPOND_LOCAL_MODE") == "true" ||
		strings.Contains(strings.ToLower(scanInput.Args), "local") ||
		output == "local"

	if localMode {
		log.Println("Executing scan in local mode using nuclei_local_runner")
		err := executeLocalScanWithSubprocess(actualTargets, scanInput.Args, requestId)
		if err != nil {
			log.Printf("Local scan failed: %v", err)
			updateScanStatus(requestId, "failed")
		} else {
			updateScanStatus(requestId, "completed")
		}
	} else {
		// Cloud mode - use existing Lambda execution
		functionName := os.Getenv("AWS_LAMBDA_FUNCTION_NAME")
		regionName := os.Getenv("AWS_REGION")
		dynamodbTable := os.Getenv("AWS_DYNAMODB_TABLE")
		if functionName == "" || regionName == "" || dynamodbTable == "" {
			log.Fatal("AWS environment variables not set for cloud mode")
		}

		core.ExecuteScans(batches, output, functionName, strings.Split(NucleiArgs, " "), threads, silent)
		updateScanStatus(requestId, "completed")
	}

	log.Println("Scan", scanId, "completed")
}

// executeLocalScanWithSubprocess runs nuclei_local_runner as a subprocess
func executeLocalScanWithSubprocess(targets []string, nucleiArgs string, requestId string) error {
	// Get the path to nuclei_local_runner executable
	nucleiRunnerPath := os.Getenv("NUCLEI_LOCAL_RUNNER_PATH")
	if nucleiRunnerPath == "" {
		// Default to looking for it in the lambda-nuclei-scanner directory
		nucleiRunnerPath = "../lambda-nuclei-scanner/nuclei_local_runner"
		if _, err := os.Stat(nucleiRunnerPath); os.IsNotExist(err) {
			// Try current directory
			nucleiRunnerPath = "./nuclei_local_runner"
			if _, err := os.Stat(nucleiRunnerPath); os.IsNotExist(err) {
				return fmt.Errorf("nuclei_local_runner executable not found. Set NUCLEI_LOCAL_RUNNER_PATH environment variable")
			}
		}
	}

	// Create temporary output file
	tempDir := os.TempDir()
	outputFile := filepath.Join(tempDir, fmt.Sprintf("nuclei_results_%s.json", requestId))
	defer os.Remove(outputFile) // Clean up after use

	// Prepare command arguments
	var cmdArgs []string

	// Add targets
	if len(targets) == 1 {
		cmdArgs = append(cmdArgs, "-targets", targets[0])
	} else {
		cmdArgs = append(cmdArgs, "-targets", strings.Join(targets, ","))
	}

	// Add nuclei arguments if provided
	if nucleiArgs != "" {
		cmdArgs = append(cmdArgs, "-args", nucleiArgs)
	}

	// Add output file
	cmdArgs = append(cmdArgs, "-output-file", outputFile)

	log.Printf("Executing nuclei_local_runner with args: %v", cmdArgs)

	// Execute the subprocess
	cmd := exec.Command(nucleiRunnerPath, cmdArgs...)

	// Capture stdout and stderr
	output, err := cmd.CombinedOutput()

	if err != nil {
		log.Printf("nuclei_local_runner execution failed: %v\nOutput: %s", err, string(output))
		return fmt.Errorf("subprocess execution failed: %v", err)
	}

	log.Printf("nuclei_local_runner completed successfully. Output: %s", string(output))

	// Read results from output file if it exists
	if _, err := os.Stat(outputFile); err == nil {
		results, readErr := os.ReadFile(outputFile)
		if readErr != nil {
			log.Printf("Warning: Could not read results file: %v", readErr)
		} else {
			log.Printf("Scan results: %s", string(results))
			// Here you could store results in DynamoDB or S3 if needed
		}
	} else {
		log.Printf("No results file generated at: %s", outputFile)
	}

	return nil
}

// Update scan status with timestamp
func updateScanStatus(requestId string, status string) error {
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(os.Getenv("AWS_REGION")),
	})
	if err != nil {
		return err
	}

	svc := dynamodb.New(sess)

	// Update scan status and updated_at timestamp
	updateInput := &dynamodb.UpdateItemInput{
		TableName: aws.String(os.Getenv("AWS_DYNAMODB_TABLE")),
		Key: map[string]*dynamodb.AttributeValue{
			"scan_id": {
				S: aws.String(requestId),
			},
		},
		UpdateExpression: aws.String("SET #status = :status, updated_at = :updated_at"),
		ExpressionAttributeNames: map[string]*string{
			"#status": aws.String("status"),
		},
		ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{
			":status": {
				S: aws.String(status),
			},
			":updated_at": {
				S: aws.String(time.Now().Format(time.RFC3339)),
			},
		},
		// Ensure the item exists before updating to prevent creation of incomplete records
		ConditionExpression: aws.String("attribute_exists(scan_id)"),
	}

	// Add completed_at if status is completed or failed
	if status == "completed" || status == "failed" {
		updateInput.UpdateExpression = aws.String("SET #status = :status, updated_at = :updated_at, completed_at = :completed_at")
		updateInput.ExpressionAttributeValues[":completed_at"] = &dynamodb.AttributeValue{
			S: aws.String(time.Now().Format(time.RFC3339)),
		}
	}

	_, err = svc.UpdateItem(updateInput)
	if err != nil {
		log.Printf("Failed to update scan status in DynamoDB: %v", err)
		return err
	}

	return nil
}

// validateScanRecord checks if a scan record has all required fields
func validateScanRecord(scan *ScanRequest) bool {
	isValid := true

	if scan.ScanID == "" || scan.RequestID == "" {
		log.Printf("Warning: Incomplete scan record found - missing scan_id or request_id")
		isValid = false
	}

	// Check if we have targets either directly or via S3
	if scan.TargetCount == 0 && scan.TargetsS3URL == "" {
		log.Printf("Warning: Scan record %s has no targets and no S3 URL", scan.ScanID)
		isValid = false
	}

	if scan.Batches == 0 || scan.Threads == 0 {
		log.Printf("Warning: Scan record %s has invalid batch/thread configuration (batches=%d, threads=%d)",
			scan.ScanID, scan.Batches, scan.Threads)
		isValid = false
	}

	// Log additional details for incomplete records
	if !isValid {
		log.Printf("Incomplete record details - ScanID: %s, RequestID: %s, Status: %s, Targets: %d, S3URL: %s, Batches: %d, Threads: %d",
			scan.ScanID, scan.RequestID, scan.Status, scan.TargetCount, scan.TargetsS3URL, scan.Batches, scan.Threads)
	}

	return isValid
}

// Retrieve all scans from DynamoDB with pagination
func getAllScans(limit int, exclusiveStartKey map[string]*dynamodb.AttributeValue, statusFilter string) (*ScanListResponse, error) {
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(os.Getenv("AWS_REGION")),
	})
	if err != nil {
		return nil, err
	}

	svc := dynamodb.New(sess)

	input := &dynamodb.ScanInput{
		TableName: aws.String(os.Getenv("AWS_DYNAMODB_TABLE")),
		Limit:     aws.Int64(int64(limit)),
	}

	if exclusiveStartKey != nil {
		input.ExclusiveStartKey = exclusiveStartKey
	}

	// Add status filter if provided
	if statusFilter != "" && statusFilter != "all" {
		input.FilterExpression = aws.String("#status = :status")
		input.ExpressionAttributeNames = map[string]*string{
			"#status": aws.String("status"),
		}
		input.ExpressionAttributeValues = map[string]*dynamodb.AttributeValue{
			":status": {
				S: aws.String(statusFilter),
			},
		}
	}

	result, err := svc.Scan(input)
	if err != nil {
		return nil, err
	}

	// Parse scan results
	scans := make([]ScanRequest, 0, len(result.Items))
	for _, item := range result.Items {
		scan, parseErr := parseScanFromDynamoDB(item)
		if parseErr != nil {
			log.Printf("Failed to parse scan from DynamoDB: %v", parseErr)
			continue
		}

		// Validate scan record completeness and log warnings
		if !validateScanRecord(scan) {
			log.Printf("Including incomplete scan record %s in response with safe defaults", scan.ScanID)
		}

		scans = append(scans, *scan)
	}

	return &ScanListResponse{
		Scans:   scans,
		Total:   len(scans),
		Limit:   limit,
		HasMore: result.LastEvaluatedKey != nil,
	}, nil
}

// Retrieve a single scan by ID
func getScanByID(scanId string) (*ScanRequest, error) {
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(os.Getenv("AWS_REGION")),
	})
	if err != nil {
		return nil, err
	}

	svc := dynamodb.New(sess)

	result, err := svc.GetItem(&dynamodb.GetItemInput{
		TableName: aws.String(os.Getenv("AWS_DYNAMODB_TABLE")),
		Key: map[string]*dynamodb.AttributeValue{
			"scan_id": {
				S: aws.String(scanId),
			},
		},
	})

	if err != nil {
		return nil, err
	}

	if result.Item == nil {
		return nil, fmt.Errorf("scan not found")
	}

	return parseScanFromDynamoDB(result.Item)
}

// Parse DynamoDB item into ScanRequest struct
func parseScanFromDynamoDB(item map[string]*dynamodb.AttributeValue) (*ScanRequest, error) {
	scan := &ScanRequest{
		// Set safe defaults for all fields
		ScanID:       "",
		RequestID:    "",
		Status:       "unknown",
		TargetCount:  0,
		TargetsS3URL: "",
		Args:         "",
		Mode:         "",
		Batches:      0,
		Threads:      0,
		Output:       "",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		TTL:          0,
	}

	if val, exists := item["scan_id"]; exists && val.S != nil {
		scan.ScanID = *val.S
	}

	if val, exists := item["request_id"]; exists && val.S != nil {
		scan.RequestID = *val.S
	}

	if val, exists := item["status"]; exists && val.S != nil {
		scan.Status = *val.S
	}

	if val, exists := item["target_count"]; exists && val.N != nil {
		if targetCount, err := fmt.Sscanf(*val.N, "%d", &scan.TargetCount); err != nil || targetCount != 1 {
			log.Printf("Failed to parse target count: %v", err)
		}
	}

	if val, exists := item["targets_s3_url"]; exists && val.S != nil {
		scan.TargetsS3URL = *val.S

		// Log S3 URL info but don't modify the stored target count
		if scan.TargetsS3URL != "" {
			log.Printf("Scan %s has targets stored in S3: %s (count: %d)", scan.ScanID, scan.TargetsS3URL, scan.TargetCount)
		}
	}

	if val, exists := item["args"]; exists && val.S != nil {
		scan.Args = *val.S
	}

	if val, exists := item["mode"]; exists && val.S != nil {
		scan.Mode = *val.S
	}

	if val, exists := item["batches"]; exists && val.N != nil {
		if batches, err := fmt.Sscanf(*val.N, "%d", &scan.Batches); err != nil || batches != 1 {
			log.Printf("Failed to parse batches: %v", err)
		}
	}

	if val, exists := item["threads"]; exists && val.N != nil {
		if threads, err := fmt.Sscanf(*val.N, "%d", &scan.Threads); err != nil || threads != 1 {
			log.Printf("Failed to parse threads: %v", err)
		}
	}

	if val, exists := item["output"]; exists && val.S != nil {
		scan.Output = *val.S
	}

	if val, exists := item["created_at"]; exists && val.S != nil {
		if createdAt, err := time.Parse(time.RFC3339, *val.S); err == nil {
			scan.CreatedAt = createdAt
		}
	}

	if val, exists := item["updated_at"]; exists && val.S != nil {
		if updatedAt, err := time.Parse(time.RFC3339, *val.S); err == nil {
			scan.UpdatedAt = updatedAt
		}
	}

	if val, exists := item["completed_at"]; exists && val.S != nil {
		if completedAt, err := time.Parse(time.RFC3339, *val.S); err == nil {
			scan.CompletedAt = &completedAt
		}
	}

	if val, exists := item["ttl"]; exists && val.N != nil {
		if ttl, err := fmt.Sscanf(*val.N, "%d", &scan.TTL); err != nil || ttl != 1 {
			log.Printf("Failed to parse TTL: %v", err)
		}
	}

	return scan, nil
}

// storeScanRequest creates a new scan record in DynamoDB (INSERT operation)
func storeScanRequest(scanRequest *ScanRequest) error {
	log.Printf("Storing new scan request in DynamoDB: %s", scanRequest.ScanID)

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(os.Getenv("AWS_REGION")),
	})
	if err != nil {
		return fmt.Errorf("failed to create AWS session: %v", err)
	}

	svc := dynamodb.New(sess)

	// Prepare the item to be put into the DynamoDB table
	item := map[string]*dynamodb.AttributeValue{
		"scan_id": {
			S: aws.String(scanRequest.ScanID),
		},
		"request_id": {
			S: aws.String(scanRequest.RequestID),
		},
		"status": {
			S: aws.String(scanRequest.Status),
		},
		"target_count": {
			N: aws.String(fmt.Sprintf("%d", scanRequest.TargetCount)),
		},
		"targets_s3_url": {
			S: aws.String(scanRequest.TargetsS3URL),
		},
		"args": {
			S: aws.String(scanRequest.Args),
		},
		"mode": {
			S: aws.String(scanRequest.Mode),
		},
		"batches": {
			N: aws.String(fmt.Sprintf("%d", scanRequest.Batches)),
		},
		"threads": {
			N: aws.String(fmt.Sprintf("%d", scanRequest.Threads)),
		},
		"output": {
			S: aws.String(scanRequest.Output),
		},
		"created_at": {
			S: aws.String(scanRequest.CreatedAt.Format(time.RFC3339)),
		},
		"updated_at": {
			S: aws.String(scanRequest.UpdatedAt.Format(time.RFC3339)),
		},
		"ttl": {
			N: aws.String(fmt.Sprintf("%d", scanRequest.TTL)),
		},
	}

	// Add completed_at only if it's set
	if scanRequest.CompletedAt != nil {
		item["completed_at"] = &dynamodb.AttributeValue{
			S: aws.String(scanRequest.CompletedAt.Format(time.RFC3339)),
		}
	}

	putInput := &dynamodb.PutItemInput{
		TableName: aws.String(os.Getenv("AWS_DYNAMODB_TABLE")),
		Item:      item,
		// Ensure we don't overwrite existing records
		ConditionExpression: aws.String("attribute_not_exists(scan_id)"),
	}

	_, err = svc.PutItem(putInput)
	if err != nil {
		return fmt.Errorf("failed to store scan request in DynamoDB: %v", err)
	}

	log.Printf("Successfully stored scan request: %s", scanRequest.ScanID)
	return nil
}

// storeTargetsInS3 stores a large target list in S3 and returns the S3 URL
func storeTargetsInS3(targets []string, requestId string) (string, error) {
	bucketName := os.Getenv("AWS_S3_BUCKET")
	if bucketName == "" {
		return "", fmt.Errorf("AWS_S3_BUCKET environment variable not set")
	}

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(os.Getenv("AWS_REGION")),
	})
	if err != nil {
		return "", fmt.Errorf("failed to create AWS session: %v", err)
	}

	svc := s3.New(sess)

	// Convert targets to JSON
	targetsJSON, err := json.Marshal(targets)
	if err != nil {
		return "", fmt.Errorf("failed to marshal targets to JSON: %v", err)
	}

	// Store in S3
	key := fmt.Sprintf("scan-targets/%s/targets.json", requestId)
	_, err = svc.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(bucketName),
		Key:         aws.String(key),
		Body:        strings.NewReader(string(targetsJSON)),
		ContentType: aws.String("application/json"),
		Metadata: map[string]*string{
			"scan-id": aws.String(requestId),
			"type":    aws.String("targets"),
		},
	})

	if err != nil {
		return "", fmt.Errorf("failed to store targets in S3: %v", err)
	}

	s3URL := fmt.Sprintf("s3://%s/%s", bucketName, key)
	log.Printf("Stored %d targets in S3: %s", len(targets), s3URL)
	return s3URL, nil
}

// retrieveTargetsFromS3 retrieves targets from S3 given an S3 URL
func retrieveTargetsFromS3(s3URL string) ([]string, error) {
	// Parse S3 URL
	if !strings.HasPrefix(s3URL, "s3://") {
		return nil, fmt.Errorf("invalid S3 URL format: %s", s3URL)
	}

	parts := strings.SplitN(strings.TrimPrefix(s3URL, "s3://"), "/", 2)
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid S3 URL format: %s", s3URL)
	}

	bucketName := parts[0]
	key := parts[1]

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(os.Getenv("AWS_REGION")),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create AWS session: %v", err)
	}

	svc := s3.New(sess)

	// Get object from S3
	result, err := svc.GetObject(&s3.GetObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(key),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve targets from S3: %v", err)
	}
	defer result.Body.Close()

	// Parse JSON
	var targets []string
	decoder := json.NewDecoder(result.Body)
	if err := decoder.Decode(&targets); err != nil {
		return nil, fmt.Errorf("failed to decode targets JSON: %v", err)
	}

	return targets, nil
}

// populateTargetsFromS3 retrieves actual targets from S3 for API responses when needed
func populateTargetsFromS3(scanRequest *ScanRequest) ([]string, error) {
	if scanRequest.TargetsS3URL == "" {
		return []string{}, nil
	}

	targets, err := retrieveTargetsFromS3(scanRequest.TargetsS3URL)
	if err != nil {
		log.Printf("Failed to retrieve targets from S3 for scan %s: %v", scanRequest.ScanID, err)
		return []string{}, err
	}

	return targets, nil
}
