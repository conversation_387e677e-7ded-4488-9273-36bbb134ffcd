package server

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
)

func setupTestRouter() *chi.Mux {
	r := chi.NewRouter()
	r.Use(middleware.Logger)
	r.Use(middleware.Recoverer)
	r.Use(corsMiddleware)

	// Routes with API key protection (except health check)
	r.Route("/", func(r chi.Router) {
		r.Use(apiKeyMiddleware)
		r.Get("/", indexHandler)
		r.Post("/scan", scanHandler)
		r.Get("/scan/{scanId}", scanStatusHandler)
	})

	// Public health check endpoint (no API key required)
	r.Get("/health-check", healthHandler)

	return r
}

func TestHealthHandler(t *testing.T) {
	router := setupTestRouter()

	req, err := http.NewRequest("GET", "/health-check", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	var response Response
	if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
		t.Errorf("Failed to unmarshal response: %v", err)
	}

	if response.Status != "ok" {
		t.Errorf("Expected status 'ok', got '%s'", response.Status)
	}
}

func TestIndexHandlerWithoutAPIKey(t *testing.T) {
	router := setupTestRouter()

	req, err := http.NewRequest("GET", "/", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusUnauthorized {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusUnauthorized)
	}
}

func TestIndexHandlerWithValidAPIKey(t *testing.T) {
	// Set up test API key
	os.Setenv("NUCLEARPOND_API_KEY", "test-api-key")
	defer os.Unsetenv("NUCLEARPOND_API_KEY")

	router := setupTestRouter()

	req, err := http.NewRequest("GET", "/", nil)
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("X-NuclearPond-API-Key", "test-api-key")

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	var response Response
	if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
		t.Errorf("Failed to unmarshal response: %v", err)
	}

	if response.Message != "Welcome to the Nuclear Pond API" {
		t.Errorf("Expected welcome message, got '%s'", response.Message)
	}
}

func TestScanHandlerValidation(t *testing.T) {
	// Set up test API key
	os.Setenv("NUCLEARPOND_API_KEY", "test-api-key")
	defer os.Unsetenv("NUCLEARPOND_API_KEY")

	router := setupTestRouter()

	// Test with empty targets
	reqBody := Request{
		Targets: []string{},
		Batches: 1,
		Threads: 1,
		Args:    "",
		Output:  "s3",
	}

	jsonBody, _ := json.Marshal(reqBody)
	req, err := http.NewRequest("POST", "/scan", bytes.NewBuffer(jsonBody))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-NuclearPond-API-Key", "test-api-key")

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusBadRequest {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusBadRequest)
	}

	var response Response
	if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
		t.Errorf("Failed to unmarshal response: %v", err)
	}

	if response.Error != "Bad Request" {
		t.Errorf("Expected 'Bad Request' error, got '%s'", response.Error)
	}
}

func TestCORSHeaders(t *testing.T) {
	router := setupTestRouter()

	req, err := http.NewRequest("OPTIONS", "/health-check", nil)
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Origin", "http://localhost:3000")

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	// Check CORS headers
	if origin := rr.Header().Get("Access-Control-Allow-Origin"); origin != "*" {
		t.Errorf("Expected Access-Control-Allow-Origin '*', got '%s'", origin)
	}

	if methods := rr.Header().Get("Access-Control-Allow-Methods"); methods != "GET, POST, OPTIONS" {
		t.Errorf("Expected Access-Control-Allow-Methods 'GET, POST, OPTIONS', got '%s'", methods)
	}
}
