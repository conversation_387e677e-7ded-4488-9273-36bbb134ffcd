package server

import (
	"log"
	"net/http"
	"os"
)

// checkAP<PERSON><PERSON><PERSON> verifies if the provided API key in the request header matches
// the NUCLEARPOND_API_KEY environment variable.
// Returns true if the API key is valid, false otherwise.
func checkAPIKey(r *http.Request) bool {
	expectedAPIKey := os.Getenv("NUCLEARPOND_API_KEY")

	if expectedAPIKey == "" {
		// This state should ideally be prevented by serverCheck at startup.
		// Logging here as an additional safeguard, though serverCheck will log.Fatal.
		log.Println("Warning: NUCLEARPOND_API_KEY is not set in the environment; API key check will consequently fail.")
		return false
	}

	requestAPIKey := r.Header.Get("X-NuclearPond-API-Key")
	if requestAPIKey == "" {
		log.Printf("API key missing in request from %s", r.RemoteAddr)
		return false
	}

	if requestAPIKey != expectedAPIKey {
		// Avoid logging the actual key received from the client for security.
		log.Printf("Invalid API key provided by client from %s", r.<PERSON><PERSON>dd<PERSON>)
		return false
	}

	return true
}

// validateAPIKeyHeader checks if the API key header is present and not empty
// This can be used for additional validation if needed
func validateAPIKeyHeader(r *http.Request) bool {
	apiKey := r.Header.Get("X-NuclearPond-API-Key")
	return apiKey != ""
}
