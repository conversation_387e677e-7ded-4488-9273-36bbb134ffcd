package cmd

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"nuclear_pond/pkg/core"
	"nuclear_pond/pkg/helpers"
	"nuclear_pond/pkg/server"

	"github.com/common-nighthawk/go-figure"
	"github.com/spf13/cobra"
)

var asciiBanner = figure.NewFigure("Fast Scan", "", true).String()

var rootCmd = &cobra.Command{
	Use:     "nuclearpond",
	Short:   "A CLI tool for Nuclear Pond to run nuclei in parallel",
	Long:    "Nuclear Pond invokes nuclei in parallel through invoking lambda functions, customizes command line flags, specifies output, and batches requests.",
	Example: `nuclearpond run -t fast-scan-demo-target.click -a $(echo -ne "-t dns" | base64) -o cmd`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println(asciiBanner)
		cmd.Help()
	},
}

var silent bool
var target string
var targets string
var nucleiArgs string
var region string
var functionName string
var batchSize int
var output string
var threads int

var runCmd = &cobra.Command{
	Use:   "run",
	Short: "Execute nuclei tasks",
	Long:  "Executes nuclei tasks in parallel by invoking lambda asynchronously",
	Run: func(cmd *cobra.Command, args []string) {
		if !silent {
			fmt.Println(asciiBanner)
			fmt.Println()
		}

		// Targets flag
		if targets == "" && target == "" {
			log.Fatal("Either a target or a list of targets is required")
			os.Exit(1)
		}

		var nucleiFlags []string
		if nucleiArgs == "" {
			nucleiFlags = []string{}
		} else {
			decodedBytes, err := base64.StdEncoding.DecodeString(nucleiArgs)
			if err != nil {
				log.Fatalf("Failed to decode nuclei args: %v", err)
			}
			decodedString := string(decodedBytes)
			if decodedString == "" {
				nucleiFlags = []string{}
			} else {
				nucleiFlags = strings.Split(decodedString, " ")
			}
		}

		if targets != "" {
			urls := helpers.ReadUrlsFromFile(targets)
			urls = helpers.RemoveEmpty(urls)
			log.Println("Running nuclear pond against", len(urls), "targets")
			batches := helpers.SplitSlice(urls, batchSize)
			log.Println("Splitting targets into", len(batches), "individual executions")
			log.Println("Running with " + fmt.Sprint(threads) + " threads")
			core.ExecuteScans(batches, output, functionName, nucleiFlags, threads, silent)
		} else {
			log.Println("Running nuclei against the target", target)
			log.Println("Running with " + fmt.Sprint(threads) + " threads")
			batches := [][]string{{target}}
			core.ExecuteScans(batches, output, functionName, nucleiFlags, threads, silent)
		}
	},
}

var startServer = &cobra.Command{
	Use:   "service",
	Short: "Launch API to launch run tasks to the nuclei runner.",
	Long:  "Executes nuclei through an API through asynchronous lambda functions",
	Run: func(cmd *cobra.Command, args []string) {
		// Print banner
		fmt.Println(asciiBanner)
		fmt.Println()
		// Start server
		log.Println("Running nuclear pond http server on port 8082")
		log.Println("http://localhost:8082")
		server.HandleRequests()
	},
}

// Variables for local run command
var apiEndpoint string

var localRunCmd = &cobra.Command{
	Use:   "local",
	Short: "Execute nuclei tasks locally via API",
	Long:  "Executes nuclei tasks locally by making API calls to the Nuclear Pond server with local mode",
	Run: func(cmd *cobra.Command, args []string) {
		if !silent {
			fmt.Println(asciiBanner)
			fmt.Println()
		}

		// Targets flag
		if targets == "" && target == "" {
			log.Fatal("Either a target or a list of targets is required")
			os.Exit(1)
		}

		// Prepare targets list
		var targetsList []string
		if targets != "" {
			urls := helpers.ReadUrlsFromFile(targets)
			targetsList = helpers.RemoveEmpty(urls)
			log.Println("Running nuclei locally against", len(targetsList), "targets")
		} else {
			targetsList = []string{target}
			log.Println("Running nuclei locally against the target", target)
		}

		// Make API call to start local scan
		scanId, err := startLocalScan(targetsList, nucleiArgs, apiEndpoint)
		if err != nil {
			log.Fatalf("Failed to start local scan: %v", err)
		}

		log.Printf("Local scan started with ID: %s", scanId)

		// Poll for scan status
		err = pollScanStatus(scanId, apiEndpoint)
		if err != nil {
			log.Fatalf("Error polling scan status: %v", err)
		}

		log.Println("Local scan completed successfully")
	},
}

// startLocalScan makes an API call to start a local scan
func startLocalScan(targets []string, args string, endpoint string) (string, error) {
	if endpoint == "" {
		endpoint = "http://localhost:8082"
	}

	// Prepare request payload
	payload := map[string]interface{}{
		"Targets": targets,
		"Batches": 1,
		"Threads": threads,
		"Args":    args,
		"Output":  "json",
		"Mode":    "local", // Explicitly set local mode
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %v", err)
	}

	// Make HTTP request
	req, err := http.NewRequest("POST", endpoint+"/scan", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	// Add API key if available
	if apiKey := os.Getenv("NUCLEARPOND_API_KEY"); apiKey != "" {
		req.Header.Set("X-NuclearPond-API-Key", apiKey)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %v", err)
	}

	if resp.StatusCode != http.StatusAccepted {
		return "", fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response to get scan ID
	var response map[string]interface{}
	err = json.Unmarshal(body, &response)
	if err != nil {
		return "", fmt.Errorf("failed to parse response: %v", err)
	}

	scanId, ok := response["RequestId"].(string)
	if !ok {
		return "", fmt.Errorf("invalid response format: missing RequestId")
	}

	return scanId, nil
}

// pollScanStatus polls the API for scan completion
func pollScanStatus(scanId string, endpoint string) error {
	if endpoint == "" {
		endpoint = "http://localhost:8082"
	}

	client := &http.Client{Timeout: 10 * time.Second}

	for {
		req, err := http.NewRequest("GET", endpoint+"/scan/"+scanId, nil)
		if err != nil {
			return fmt.Errorf("failed to create status request: %v", err)
		}

		// Add API key if available
		if apiKey := os.Getenv("NUCLEARPOND_API_KEY"); apiKey != "" {
			req.Header.Set("X-NuclearPond-API-Key", apiKey)
		}

		resp, err := client.Do(req)
		if err != nil {
			return fmt.Errorf("failed to check status: %v", err)
		}

		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			return fmt.Errorf("failed to read status response: %v", err)
		}

		if resp.StatusCode != http.StatusOK {
			return fmt.Errorf("status check failed with status %d: %s", resp.StatusCode, string(body))
		}

		var response map[string]interface{}
		err = json.Unmarshal(body, &response)
		if err != nil {
			return fmt.Errorf("failed to parse status response: %v", err)
		}

		status, ok := response["Status"].(string)
		if !ok {
			return fmt.Errorf("invalid status response format")
		}

		log.Printf("Scan status: %s", status)

		switch status {
		case "completed":
			return nil
		case "failed":
			return fmt.Errorf("scan failed")
		case "running":
			time.Sleep(5 * time.Second) // Wait 5 seconds before next poll
			continue
		default:
			return fmt.Errorf("unknown scan status: %s", status)
		}
	}
}

func init() {
	// run subcommand
	// Mark flags as required
	runCmd.MarkFlagRequired("output")
	// General flags
	runCmd.Flags().BoolVarP(&silent, "silent", "s", false, "silent command line output")
	runCmd.Flags().StringVarP(&target, "target", "t", "", "individual target to specify")
	runCmd.Flags().StringVarP(&targets, "targets", "l", "", "list of targets in a file")
	runCmd.Flags().StringVarP(&nucleiArgs, "args", "a", "", "nuclei arguments as base64 encoded string")
	runCmd.Flags().IntVarP(&batchSize, "batch-size", "b", 1, "batch size for number of targets per execution")
	runCmd.Flags().StringVarP(&output, "output", "o", "json", "output type to save nuclei results(s3, cmd, or json)")
	runCmd.Flags().IntVarP(&threads, "threads", "c", 1, "number of threads to run lambda functions, default is 1 which will be slow")
	// Region flag
	runCmd.Flags().StringVarP(&region, "region", "r", "", "AWS region to run nuclei")
	if region == "" {
		region, ok := os.LookupEnv("AWS_REGION")
		if !ok {
			runCmd.MarkFlagRequired("region")
		} else {
			runCmd.Flags().Set("region", region)
		}
	}
	// Function name flag
	runCmd.Flags().StringVarP(&functionName, "function-name", "f", "", "AWS Lambda function name")
	if functionName == "" {
		functionName, ok := os.LookupEnv("AWS_LAMBDA_FUNCTION_NAME")
		if !ok {
			runCmd.MarkFlagRequired("function-name")
		} else {
			runCmd.Flags().Set("function-name", functionName)
		}
	}

	// local run subcommand
	// General flags
	localRunCmd.Flags().BoolVarP(&silent, "silent", "s", false, "silent command line output")
	localRunCmd.Flags().StringVarP(&target, "target", "t", "", "individual target to specify")
	localRunCmd.Flags().StringVarP(&targets, "targets", "l", "", "list of targets in a file")
	localRunCmd.Flags().StringVarP(&nucleiArgs, "args", "a", "", "nuclei arguments as base64 encoded string")
	localRunCmd.Flags().IntVarP(&batchSize, "batch-size", "b", 1, "batch size for number of targets per execution")
	localRunCmd.Flags().IntVarP(&threads, "threads", "c", 4, "number of threads to run nuclei in parallel (default 4)")
	localRunCmd.Flags().StringVarP(&apiEndpoint, "api-endpoint", "e", "http://localhost:8082", "Nuclear Pond API endpoint")
}

// Execute executes the root command.
func Execute() error {
	rootCmd.CompletionOptions.DisableDefaultCmd = true
	rootCmd.SetHelpCommand(&cobra.Command{
		Use:    "no-help",
		Hidden: true,
	})

	rootCmd.HasHelpSubCommands()
	rootCmd.AddCommand(runCmd)
	rootCmd.AddCommand(startServer)
	rootCmd.AddCommand(localRunCmd)

	return rootCmd.Execute()
}
