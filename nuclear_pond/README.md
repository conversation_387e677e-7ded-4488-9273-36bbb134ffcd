# Nuclear Pond

Nuclear Pond is a high-performance backend tool for Fast Scan project. It offers flexible nuclei execution modes, including local parallel processing and cloud-based scanning via AWS Lambda, along with an HTTP API for programmatic control.

## Architecture Overview

Nuclear Pond's architecture supports distinct operational modes:
- **Local Mode**: Direct execution of Nuclei, orchestrated for parallelism.
- **Cloud Mode**: Utilizes AWS Lambda for distributed scanning, S3 for results, and DynamoDB for state management (when used with the API server).
- **API Service**: An HTTP server that can manage and initiate scans in either local or cloud mode, providing a persistent control plane.

For a more detailed explanation of the system architecture, please see the [Architecture Overview](docs/architecture.md) document.
The AWS infrastructure is managed via Terraform; for more details, refer to the [Terraform Setup Overview](../terraform/README.md).

## Documentation Hub

For complete information on installation, usage, deployment, and API integration, please see:
- **[Getting Started Guide](docs/getting-started.md)**: Installation and your first scan.
- **[User Guide](docs/user-guide.md)**: Comprehensive CLI usage.
- **[API Reference](docs/api-reference.md)**: Programmatic access via HTTP API.
- **[Deployment Guide](../terraform/nuclear_pond_backend/README.md)**: Deploying Nuclear Pond on AWS.


## Security Considerations

> ⚠️ **Important**: Nuclear Pond passes arguments directly to Nuclei for maximum flexibility. This provides power but requires careful input validation when used programmatically, especially through the API. Always sanitize inputs and use appropriate security controls for your environment. Further security best practices for deployment are covered in the [Deployment Guide](../terraform/nuclear_pond_backend/README.md).


