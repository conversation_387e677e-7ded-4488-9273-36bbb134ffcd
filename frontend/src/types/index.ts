// Authentication types
export interface User {
  id: string;
  email: string;
  name: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Scan types
export interface ScanTarget {
  domain: string;
}

export interface ScanConfig {
  target_count: number; // Number of targets in the scan
  targets: string[];    // Actual targets (may be empty for performance, populated on demand)
  batches: number;
  threads: number;
  output: 's3'
}

export interface ScanRequest {
  requestId: string;
  timestamp: string;
  status: 'queued' | 'running' | 'completed' | 'failed';
  config: ScanConfig;
}

export interface Vulnerability {
  id: string;
  target: string;
  title: string;
  description: string;
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info';
  discoveredAt: string;
  remediation?: string;
}

export interface ScanResult {
  requestId: string;
  timestamp: string;
  status: 'completed' | 'failed';
  summary: {
    totalVulnerabilities: number;
    criticalCount: number;
    highCount: number;
    mediumCount: number;
    lowCount: number;
    infoCount: number;
  };
  vulnerabilities: Vulnerability[];
}

// Enhanced scan status details matching backend response
export interface ScanStatusDetails {
  scanId: string;
  requestId: string;
  status: 'queued' | 'running' | 'completed' | 'failed';
  config: ScanConfig;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  message?: string;
  error?: string;
}

// Scan results details matching backend response
export interface ScanResultsDetails {
  scanId: string;
  requestId: string;
  status: string;
  config: ScanConfig;
  createdAt: string;
  completedAt?: string;
  resultsSummary?: {
    totalFindings: number;
    criticalCount: number;
    highCount: number;
    mediumCount: number;
    lowCount: number;
    infoCount: number;
    targetsScanned: number;
    durationSeconds: number;
  };
  resultsLocation?: string;
  findings?: Record<string, unknown>[];
}

// Scan status enumeration
export type ScanStatus = 'queued' | 'running' | 'completed' | 'failed' | 'unknown';

