import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeftIcon, ExternalLinkIcon, RefreshCwIcon } from 'lucide-react';
import { useScanStatus, useScanResults } from '@/hooks/use-api';
import AppLayout from '../components/Layout/AppLayout';

const ResultDetails: React.FC = () => {
  const { scanId } = useParams<{ scanId: string }>();

  // Get scan status and results
  const { 
    data: scanStatus, 
    isLoading: statusLoading, 
    error: statusError,
    refetch: refetchStatus 
  } = useScanStatus(scanId);
  
  const { 
    data: scanResults, 
    isLoading: resultsLoading, 
    error: resultsError 
  } = useScanResults(scanId, scanStatus?.status === 'completed');

  // Render status badge
  const renderScanStatusBadge = (status: string) => {
    switch (status) {
      case 'running':
        return <Badge variant="outline" className="status-running">Running</Badge>;
      case 'completed':
        return <Badge variant="outline" className="status-completed">Completed</Badge>;
      case 'failed':
        return <Badge variant="outline" className="status-failed">Failed</Badge>;
      case 'queued':
        return <Badge variant="outline" className="text-muted-foreground">Queued</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (statusError) {
    return (
      <AppLayout>
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button asChild variant="ghost" size="sm">
              <Link to="/results">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to Results
              </Link>
            </Button>
          </div>

          <Card>
            <CardContent className="p-8 text-center">
              <div className="mx-auto w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center mb-4">
                <ExternalLinkIcon className="h-6 w-6 text-destructive" />
              </div>
              <h3 className="text-lg font-medium">Scan not found</h3>
              <p className="text-sm text-muted-foreground mt-2">
                The scan with ID "{scanId}" could not be found or you don't have access to it.
              </p>
              <Button asChild className="mt-4">
                <Link to="/results">Back to Results</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button asChild variant="ghost" size="sm">
              <Link to="/results">
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to Results
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Scan Details</h1>
              <p className="text-muted-foreground">
                {scanId ? `Scan ID: ${scanId}` : 'Loading...'}
              </p>
            </div>
          </div>
          
          {scanStatus?.status === 'running' && (
            <Button onClick={() => refetchStatus()} variant="outline" size="sm">
              <RefreshCwIcon className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          )}
        </div>

        {/* Scan Status Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Scan Information
              {statusLoading ? (
                <Skeleton className="h-6 w-20" />
              ) : scanStatus ? (
                renderScanStatusBadge(scanStatus.status)
              ) : null}
            </CardTitle>
            <CardDescription>
              Basic scan configuration and status information
            </CardDescription>
          </CardHeader>
          <CardContent>
            {statusLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            ) : scanStatus ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Status</label>
                    <div className="mt-1">
                      {renderScanStatusBadge(scanStatus.status)}
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Targets ({scanStatus.config?.targets?.length || 0})</label>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {scanStatus.config?.targets?.length > 0 ? (
                        scanStatus.config.targets.map((target, i) => (
                          <Badge key={i} variant="outline" className="text-xs">
                            {target}
                          </Badge>
                        ))
                      ) : (
                        <Badge variant="outline" className="text-xs text-muted-foreground">
                          No targets data available
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Configuration</label>
                    <div className="mt-1 text-sm">
                      <div>Batch Size: {scanStatus.config?.batches || 'N/A'}</div>
                      <div>Concurrency: {scanStatus.config?.threads || 'N/A'}</div>
                      <div>Output: {scanStatus.config?.output || 'N/A'}</div>
                      <div>Mode: {scanStatus.config?.mode || 'N/A'}</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Timeline</label>
                    <div className="mt-1 text-sm space-y-1">
                      <div>Created: {new Date(scanStatus.created_at).toLocaleString()}</div>
                      <div>Updated: {new Date(scanStatus.updated_at).toLocaleString()}</div>
                      {scanStatus.completed_at && (
                        <div>Completed: {new Date(scanStatus.completed_at).toLocaleString()}</div>
                      )}
                    </div>
                  </div>

                  {scanStatus.error && (
                    <div>
                      <label className="text-sm font-medium text-destructive">Error</label>
                      <div className="mt-1 text-sm text-destructive">
                        {scanStatus.error}
                      </div>
                    </div>
                  )}

                  {scanStatus.message && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Message</label>
                      <div className="mt-1 text-sm">
                        {scanStatus.message}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : null}
          </CardContent>
        </Card>

        {/* Results Card */}
        {scanStatus?.status === 'completed' && (
          <Card>
            <CardHeader>
              <CardTitle>Scan Results</CardTitle>
              <CardDescription>
                Detailed findings and security analysis results
              </CardDescription>
            </CardHeader>
            <CardContent>
              {resultsLoading ? (
                <div className="space-y-4">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              ) : resultsError ? (
                <div className="text-center p-8">
                  <div className="mx-auto w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center mb-4">
                    <ExternalLinkIcon className="h-6 w-6 text-destructive" />
                  </div>
                  <h3 className="text-lg font-medium">Error loading results</h3>
                  <p className="text-sm text-muted-foreground mt-2">
                    {resultsError instanceof Error ? resultsError.message : 'Could not load scan results'}
                  </p>
                </div>
              ) : scanResults ? (
                <div className="space-y-6">
                  {/* Results Summary */}
                  {scanResults.results_summary && (
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground mb-3">Summary</h4>
                      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-destructive">
                            {scanResults.results_summary.critical_count}
                          </div>
                          <div className="text-xs text-muted-foreground">Critical</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-500">
                            {scanResults.results_summary.high_count}
                          </div>
                          <div className="text-xs text-muted-foreground">High</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-yellow-500">
                            {scanResults.results_summary.medium_count}
                          </div>
                          <div className="text-xs text-muted-foreground">Medium</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-500">
                            {scanResults.results_summary.low_count}
                          </div>
                          <div className="text-xs text-muted-foreground">Low</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-gray-500">
                            {scanResults.results_summary.info_count}
                          </div>
                          <div className="text-xs text-muted-foreground">Info</div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Results Location */}
                  {scanResults.results_location && (
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground mb-3">Results Location</h4>
                      <div className="p-3 bg-muted rounded-md font-mono text-sm">
                        {scanResults.results_location}
                      </div>
                    </div>
                  )}

                  {/* TODO: Add detailed findings display when implemented */}
                  <div className="p-8 text-center bg-muted/50 rounded-lg">
                    <h4 className="text-lg font-medium mb-2">Detailed Results Coming Soon</h4>
                    <p className="text-sm text-muted-foreground">
                      Detailed vulnerability findings display will be implemented in a future update.
                      For now, results are available at the location shown above.
                    </p>
                  </div>
                </div>
              ) : null}
            </CardContent>
          </Card>
        )}

        {/* Running/Queued State */}
        {(scanStatus?.status === 'running' || scanStatus?.status === 'queued') && (
          <Card>
            <CardContent className="p-8 text-center">
              <RefreshCwIcon className="h-12 w-12 mx-auto text-muted-foreground animate-spin mb-4" />
              <h3 className="text-lg font-medium">
                {scanStatus.status === 'running' ? 'Scan in Progress' : 'Scan Queued'}
              </h3>
              <p className="text-sm text-muted-foreground mt-2">
                {scanStatus.status === 'running' 
                  ? 'Your scan is currently running. Results will appear here when complete.'
                  : 'Your scan is queued for processing and will start shortly.'
                }
              </p>
            </CardContent>
          </Card>
        )}

        {/* Failed State */}
        {scanStatus?.status === 'failed' && (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="mx-auto w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center mb-4">
                <ExternalLinkIcon className="h-6 w-6 text-destructive" />
              </div>
              <h3 className="text-lg font-medium">Scan Failed</h3>
              <p className="text-sm text-muted-foreground mt-2">
                {scanStatus.error || 'The scan encountered an error and could not complete.'}
              </p>
              <Button asChild className="mt-4">
                <Link to="/scan">Start a New Scan</Link>
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
};

export default ResultDetails; 