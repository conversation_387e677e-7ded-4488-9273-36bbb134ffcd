import { <PERSON>an<PERSON>onfig, <PERSON>an<PERSON><PERSON>ult } from "../types";

// Get the API URL from environment variables
const apiUrl = import.meta.env.VITE_API_URL;

const apiKey = import.meta.env.VITE_API_KEY;

// Check if API URL is defined
if (!apiUrl) {
  console.warn("API URL is not defined in environment variables. Set VITE_API_URL in your .env file.");
}

const getApiKey = (): string | null => {
  const authData = localStorage.getItem('auth');
  console.log("authData", authData);
  if (!authData || !apiKey) return null;

  return apiKey;
};

// Helper function to handle API responses
const handleResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || `API error: ${response.status}`);
  }
  
  return response.json() as Promise<T>;
};

// Enhanced API response types to match backend
export interface BackendScanRequest {
  scan_id: string;
  request_id: string;
  status: 'queued' | 'running' | 'completed' | 'failed';
  target_count: number;  // Number of targets
  targets: string[];     // Actual targets (may be empty for performance)
  args: string;
  mode: string;
  batches: number;
  threads: number;
  output: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  ttl: number;
}

export interface BackendScanListResponse {
  scans: BackendScanRequest[];
  total: number;
  limit: number;
  offset: number;
  has_more: boolean;
}

export interface BackendScanStatusResponse {
  scan_id: string;
  request_id: string;
  status: string;
  config: {
    target_count: number;  // Number of targets
    targets: string[];     // Actual targets (may be empty for performance)
    batches: number;
    threads: number;
    output: string;
    mode: string;
    args: string;
  };
  created_at: string;
  updated_at: string;
  completed_at?: string;
  message?: string;
  error?: string;
}

export interface BackendScanResultsResponse {
  scan_id: string;
  request_id: string;
  status: string;
  config: {
    target_count: number;  // Number of targets
    targets: string[];     // Actual targets (may be empty for performance)
    batches: number;
    threads: number;
    output: string;
    mode: string;
    args: string;
  };
  created_at: string;
  completed_at?: string;
  results_summary?: {
    total_findings: number;
    critical_count: number;
    high_count: number;
    medium_count: number;
    low_count: number;
    info_count: number;
    targets_scanned: number;
    duration_seconds: number;
  };
  results_location?: string;
  findings?: Record<string, unknown>[];
}

// API client for scans
export const scanApi = {
  // Initiate a new scan
  initiateScan: async (config: ScanConfig): Promise<{ RequestId: string }> => {
    const apiKey = getApiKey();
    
    // Format the request body according to the API spec
    const requestBody = {
      Targets: config.targets,
      Batches: config.batches,
      Threads: config.threads,
      Output: config.output,
      Args: "", // Optional, can be added if needed
      Mode: "cloud", // Default to cloud mode
    };
    
    const response = await fetch(`${apiUrl}/scan`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-NuclearPond-API-Key': apiKey || '',
      },
      body: JSON.stringify(requestBody),
    });
    
    return handleResponse<{ RequestId: string }>(response);
  },
  
  // Get scan status with comprehensive information
  getScanStatus: async (scanId: string): Promise<BackendScanStatusResponse> => {
    const apiKey = getApiKey();
    
    const response = await fetch(`${apiUrl}/scan/${scanId}`, {
      method: 'GET',
      headers: {
        'X-NuclearPond-API-Key': apiKey || '',
      },
    });
    
    return handleResponse<BackendScanStatusResponse>(response);
  },
  
  // Get all scans with optional filtering
  getAllScans: async (options?: { 
    limit?: number; 
    status?: string; 
  }): Promise<BackendScanListResponse> => {
    const apiKey = getApiKey();
    
    const params = new URLSearchParams();
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.status && options.status !== 'all') params.append('status', options.status);
    
    const url = `${apiUrl}/scans${params.toString() ? `?${params.toString()}` : ''}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-NuclearPond-API-Key': apiKey || '',
      },
    });
    
    return handleResponse<BackendScanListResponse>(response);
  },
  
  // Get scan results
  getScanResults: async (scanId: string): Promise<BackendScanResultsResponse> => {
    const apiKey = getApiKey();
    
    const response = await fetch(`${apiUrl}/scan/${scanId}/results`, {
      method: 'GET',
      headers: {
        'X-NuclearPond-API-Key': apiKey || '',
      },
    });
    
    return handleResponse<BackendScanResultsResponse>(response);
  },
};

