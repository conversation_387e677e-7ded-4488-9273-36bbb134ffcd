{
    "name": "Fast Scan Dev",
    "image": "mcr.microsoft.com/devcontainers/go:1.22",
    "features": {
        "ghcr.io/devcontainers/features/terraform:1": {},
        "ghcr.io/devcontainers/features/aws-cli:1": {},
        "ghcr.io/devcontainers/features/node:1": {},
        "ghcr.io/devcontainers/features/docker-from-docker:1": {},
        "ghcr.io/devcontainers/features/sshd:1": {}
    },
    // Forward AWS credentials from WSL host
    "mounts": [
        "source=${env:HOME}${env:USERPROFILE}/.aws,target=/home/<USER>/.aws,type=bind,consistency=cached"
    ],
    // Forward Git credentials using SSH Agent Forwarding
    "runArgs": [
        "-u",
        "vscode"
    ],
    "remoteUser": "vscode",
    "forwardPorts": [22],
    "postCreateCommand": "git config --global --add safe.directory ${containerWorkspaceFolder} && mkdir -p /home/<USER>/.ssh && sudo chown -R vscode:vscode ${containerWorkspaceFolder} && corepack enable",
    // Point Go tools to workspace
    "workspaceFolder": "/workspaces/${localWorkspaceFolderBasename}",
    "workspaceMount": "source=${localWorkspaceFolder},target=/workspaces/${localWorkspaceFolderBasename},type=bind",
    "customizations": {
        "vscode": {
            "extensions": [
                "biomejs.biome"
            ]
        }
    }
}